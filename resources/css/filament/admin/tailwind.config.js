import preset from '../../../../vendor/filament/filament/tailwind.config.preset'
const colors = require("tailwindcss/colors");
const defaultTheme = require("tailwindcss/defaultTheme");
export default {
    presets: [preset],
    content: [
        "./resources/**/*.blade.php",
        "./app/Filament/**/*.php",
        "./resources/views/filament/**/*.blade.php",
        './vendor/filament/**/*.blade.php',
        "./vendor/awcodes/filament-table-repeater/resources/**/*.blade.php",
        './vendor/cmsmaxinc/filament-error-pages/resources/**/*.blade.php',
        './vendor/guava/filament-knowledge-base/src/**/*.php',
        './vendor/guava/filament-knowledge-base/resources/**/*.blade.php',
    ],
   
    plugins: [
        
        require("@tailwindcss/forms"),
        require("@tailwindcss/typography"),
    ],
}
