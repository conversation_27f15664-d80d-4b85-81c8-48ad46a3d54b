<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\User;
use App\Models\UserSession;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\Request;
use Carbon\Carbon;

final class SessionManagementService
{
    public function __construct(
        private Request $request
    ) {}

    public function createSession(User $user): UserSession
    {
        // Invalidate old sessions if max sessions exceeded
        $this->enforceMaxSessions($user);

        return UserSession::create([
            'user_id' => $user->id,
            'session_id' => Session::getId(),
            'ip_address' => $this->request->ip(),
            'user_agent' => $this->request->userAgent(),
            'login_at' => now(),
            'last_activity' => now(),
            'is_active' => true,
        ]);
    }

    public function updateSessionActivity(?string $sessionId = null): void
    {
        $sessionId = $sessionId ?? Session::getId();
        
        UserSession::where('session_id', $sessionId)
            ->update(['last_activity' => now()]);
    }

    public function endSession(?string $sessionId = null): void
    {
        $sessionId = $sessionId ?? Session::getId();
        
        UserSession::where('session_id', $sessionId)
            ->update([
                'logout_at' => now(),
                'is_active' => false,
            ]);
    }

    public function endAllUserSessions(User $user, ?string $exceptSessionId = null): int
    {
        $query = UserSession::where('user_id', $user->id)
            ->where('is_active', true);

        if ($exceptSessionId) {
            $query->where('session_id', '!=', $exceptSessionId);
        }

        return $query->update([
            'logout_at' => now(),
            'is_active' => false,
        ]);
    }

    public function getActiveSessions(User $user): \Illuminate\Database\Eloquent\Collection
    {
        return UserSession::where('user_id', $user->id)
            ->where('is_active', true)
            ->orderBy('last_activity', 'desc')
            ->get();
    }

    public function getSessionHistory(User $user, int $limit = 50): \Illuminate\Database\Eloquent\Collection
    {
        return UserSession::where('user_id', $user->id)
            ->orderBy('login_at', 'desc')
            ->limit($limit)
            ->get();
    }

    public function isSessionExpired(UserSession $session): bool
    {
        $timeout = config('session.lifetime', 120); // minutes
        return $session->last_activity->diffInMinutes(now()) > $timeout;
    }

    public function cleanupExpiredSessions(): int
    {
        $timeout = config('session.lifetime', 120); // minutes
        $cutoff = Carbon::now()->subMinutes($timeout);

        return UserSession::where('is_active', true)
            ->where('last_activity', '<', $cutoff)
            ->update([
                'logout_at' => now(),
                'is_active' => false,
            ]);
    }

    public function cleanupOldSessions(int $days = 30): int
    {
        return UserSession::where('login_at', '<', Carbon::now()->subDays($days))
            ->delete();
    }

    public function getSessionInfo(?string $sessionId = null): ?UserSession
    {
        $sessionId = $sessionId ?? Session::getId();
        
        return UserSession::where('session_id', $sessionId)->first();
    }

    public function isSessionFromSuspiciousLocation(UserSession $session, User $user): bool
    {
        // Get user's recent sessions from last 30 days
        $recentSessions = UserSession::where('user_id', $user->id)
            ->where('login_at', '>=', Carbon::now()->subDays(30))
            ->where('id', '!=', $session->id)
            ->get();

        if ($recentSessions->isEmpty()) {
            return false; // No history to compare
        }

        // Check if IP is from a different country/region
        $currentLocation = $this->getLocationFromIp($session->ip_address);
        
        foreach ($recentSessions as $recentSession) {
            $recentLocation = $this->getLocationFromIp($recentSession->ip_address);
            
            if ($currentLocation['country'] === $recentLocation['country']) {
                return false; // Found a session from same country
            }
        }

        return true; // All recent sessions from different countries
    }

    public function detectConcurrentSessions(User $user): array
    {
        $activeSessions = $this->getActiveSessions($user);
        
        if ($activeSessions->count() <= 1) {
            return [];
        }

        $suspicious = [];
        
        foreach ($activeSessions as $session) {
            // Check for sessions from different IPs at the same time
            $concurrentSessions = $activeSessions->where('ip_address', '!=', $session->ip_address)
                ->where('last_activity', '>=', $session->last_activity->subMinutes(5));
            
            if ($concurrentSessions->isNotEmpty()) {
                $suspicious[] = [
                    'session' => $session,
                    'concurrent_with' => $concurrentSessions->toArray(),
                ];
            }
        }

        return $suspicious;
    }

    public function getDeviceInfo(string $userAgent): array
    {
        // Simple user agent parsing - in production, use a proper library
        $device = 'Unknown';
        $browser = 'Unknown';
        $os = 'Unknown';

        // Detect OS
        if (str_contains($userAgent, 'Windows')) {
            $os = 'Windows';
        } elseif (str_contains($userAgent, 'Mac')) {
            $os = 'macOS';
        } elseif (str_contains($userAgent, 'Linux')) {
            $os = 'Linux';
        } elseif (str_contains($userAgent, 'Android')) {
            $os = 'Android';
        } elseif (str_contains($userAgent, 'iOS')) {
            $os = 'iOS';
        }

        // Detect Browser
        if (str_contains($userAgent, 'Chrome')) {
            $browser = 'Chrome';
        } elseif (str_contains($userAgent, 'Firefox')) {
            $browser = 'Firefox';
        } elseif (str_contains($userAgent, 'Safari')) {
            $browser = 'Safari';
        } elseif (str_contains($userAgent, 'Edge')) {
            $browser = 'Edge';
        }

        // Detect Device Type
        if (str_contains($userAgent, 'Mobile')) {
            $device = 'Mobile';
        } elseif (str_contains($userAgent, 'Tablet')) {
            $device = 'Tablet';
        } else {
            $device = 'Desktop';
        }

        return [
            'device' => $device,
            'browser' => $browser,
            'os' => $os,
        ];
    }

    private function enforceMaxSessions(User $user): void
    {
        $maxSessions = config('auth.max_sessions_per_user', 5);
        $activeSessions = $this->getActiveSessions($user);

        if ($activeSessions->count() >= $maxSessions) {
            // End the oldest sessions
            $sessionsToEnd = $activeSessions->sortBy('last_activity')
                ->take($activeSessions->count() - $maxSessions + 1);

            foreach ($sessionsToEnd as $session) {
                $this->endSession($session->session_id);
            }
        }
    }

    private function getLocationFromIp(string $ip): array
    {
        // Placeholder for IP geolocation
        // In production, use a service like MaxMind GeoIP2
        return [
            'country' => 'Unknown',
            'region' => 'Unknown',
            'city' => 'Unknown',
        ];
    }

    public function getSessionStatistics(User $user): array
    {
        $activeSessions = $this->getActiveSessions($user);
        $totalSessions = UserSession::where('user_id', $user->id)->count();
        $recentSessions = UserSession::where('user_id', $user->id)
            ->where('login_at', '>=', Carbon::now()->subDays(30))
            ->count();

        return [
            'active_sessions' => $activeSessions->count(),
            'total_sessions' => $totalSessions,
            'recent_sessions' => $recentSessions,
            'unique_ips' => UserSession::where('user_id', $user->id)
                ->distinct('ip_address')
                ->count(),
            'last_login' => $user->last_login_at,
            'current_session_duration' => $this->getCurrentSessionDuration(),
        ];
    }

    private function getCurrentSessionDuration(): ?int
    {
        $currentSession = $this->getSessionInfo();
        
        if (!$currentSession) {
            return null;
        }

        return $currentSession->login_at->diffInMinutes(now());
    }

    public function shouldRequireReauthentication(User $user): bool
    {
        $currentSession = $this->getSessionInfo();
        
        if (!$currentSession) {
            return true;
        }

        // Require re-auth for sensitive operations after 15 minutes
        return $currentSession->last_activity->diffInMinutes(now()) > 15;
    }

    public function markSessionAsReauthenticated(?string $sessionId = null): void
    {
        $sessionId = $sessionId ?? Session::getId();
        
        UserSession::where('session_id', $sessionId)
            ->update(['last_activity' => now()]);
    }
}
