<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\User;
use App\Models\UserActivityLog;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

final class UserActivityLogger
{
    public function __construct(
        private Request $request
    ) {}

    public function logLogin(User $user, bool $successful = true, ?string $reason = null): UserActivityLog
    {
        if ($successful) {
            // Update user's last login information
            $user->recordSuccessfulLogin($this->request->ip());
        } else {
            // Record failed login attempt
            $user->recordLoginAttempt($this->request->ip());
        }

        return UserActivityLog::logLogin($user, $successful, $reason);
    }

    public function logLogout(?User $user = null): ?UserActivityLog
    {
        $user = $user ?? Auth::user();
        
        if (!$user) {
            return null;
        }

        return UserActivityLog::logLogout($user);
    }

    public function logPasswordChange(User $user): UserActivityLog
    {
        return UserActivityLog::logPasswordChange($user);
    }

    public function logProfileUpdate(User $user, array $changes = []): UserActivityLog
    {
        // Check if profile is now complete
        if ($user->getProfileCompletionPercentage() >= 70 && !$user->isProfileComplete()) {
            $user->markProfileAsComplete();
        }

        return UserActivityLog::logProfileUpdate($user, $changes);
    }

    public function logAccountStatusChange(User $user, string $oldStatus, string $newStatus, ?User $changedBy = null): UserActivityLog
    {
        return UserActivityLog::logAccountStatusChange($user, $oldStatus, $newStatus, $changedBy);
    }

    public function logRoleChange(User $user, string $action, string $roleName, ?User $changedBy = null): UserActivityLog
    {
        return UserActivityLog::logRoleChange($user, $action, $roleName, $changedBy);
    }

    public function logRecordAction(string $action, Model $record, ?User $user = null): ?UserActivityLog
    {
        $user = $user ?? Auth::user();
        
        if (!$user) {
            return null;
        }

        return UserActivityLog::logRecordAction($user, $action, $record);
    }

    public function logSystemAccess(?User $user = null): ?UserActivityLog
    {
        $user = $user ?? Auth::user();
        
        if (!$user) {
            return null;
        }

        return UserActivityLog::log(
            $user->id,
            UserActivityLog::TYPE_SYSTEM_ACCESS,
            "User {$user->name} accessed the system",
            User::class,
            $user->id
        );
    }

    public function logApiAccess(?User $user = null): ?UserActivityLog
    {
        $user = $user ?? Auth::user();
        
        if (!$user) {
            return null;
        }

        return UserActivityLog::log(
            $user->id,
            UserActivityLog::TYPE_API_ACCESS,
            "User {$user->name} accessed the API",
            User::class,
            $user->id
        );
    }

    public function logFileUpload(string $filename, ?User $user = null): ?UserActivityLog
    {
        $user = $user ?? Auth::user();
        
        if (!$user) {
            return null;
        }

        return UserActivityLog::log(
            $user->id,
            UserActivityLog::TYPE_FILE_UPLOADED,
            "User {$user->name} uploaded file: {$filename}",
            null,
            null,
            ['filename' => $filename]
        );
    }

    public function logFileDownload(string $filename, ?User $user = null): ?UserActivityLog
    {
        $user = $user ?? Auth::user();
        
        if (!$user) {
            return null;
        }

        return UserActivityLog::log(
            $user->id,
            UserActivityLog::TYPE_FILE_DOWNLOADED,
            "User {$user->name} downloaded file: {$filename}",
            null,
            null,
            ['filename' => $filename]
        );
    }

    public function logDataExport(string $type, int $recordCount, ?User $user = null): ?UserActivityLog
    {
        $user = $user ?? Auth::user();
        
        if (!$user) {
            return null;
        }

        return UserActivityLog::log(
            $user->id,
            UserActivityLog::TYPE_DATA_EXPORTED,
            "User {$user->name} exported {$recordCount} {$type} records",
            null,
            null,
            ['export_type' => $type, 'record_count' => $recordCount]
        );
    }

    public function logDataImport(string $type, int $recordCount, ?User $user = null): ?UserActivityLog
    {
        $user = $user ?? Auth::user();
        
        if (!$user) {
            return null;
        }

        return UserActivityLog::log(
            $user->id,
            UserActivityLog::TYPE_DATA_IMPORTED,
            "User {$user->name} imported {$recordCount} {$type} records",
            null,
            null,
            ['import_type' => $type, 'record_count' => $recordCount]
        );
    }

    public function logBulkAction(string $action, string $modelType, int $recordCount, ?User $user = null): ?UserActivityLog
    {
        $user = $user ?? Auth::user();
        
        if (!$user) {
            return null;
        }

        return UserActivityLog::log(
            $user->id,
            UserActivityLog::TYPE_BULK_ACTION,
            "User {$user->name} performed bulk {$action} on {$recordCount} {$modelType} records",
            null,
            null,
            ['action' => $action, 'model_type' => $modelType, 'record_count' => $recordCount]
        );
    }

    public function logEmailVerification(User $user): UserActivityLog
    {
        return UserActivityLog::log(
            $user->id,
            UserActivityLog::TYPE_EMAIL_VERIFIED,
            "User {$user->name} verified their email address",
            User::class,
            $user->id
        );
    }

    public function logPhoneVerification(User $user): UserActivityLog
    {
        return UserActivityLog::log(
            $user->id,
            UserActivityLog::TYPE_PHONE_VERIFIED,
            "User {$user->name} verified their phone number",
            User::class,
            $user->id
        );
    }

    public function logTwoFactorToggle(User $user, bool $enabled): UserActivityLog
    {
        $type = $enabled ? UserActivityLog::TYPE_TWO_FACTOR_ENABLED : UserActivityLog::TYPE_TWO_FACTOR_DISABLED;
        $action = $enabled ? 'enabled' : 'disabled';
        
        return UserActivityLog::log(
            $user->id,
            $type,
            "User {$user->name} {$action} two-factor authentication",
            User::class,
            $user->id
        );
    }

    public function logSettingsUpdate(User $user, array $changes = []): UserActivityLog
    {
        return UserActivityLog::log(
            $user->id,
            UserActivityLog::TYPE_SETTINGS_UPDATED,
            "User {$user->name} updated their settings",
            User::class,
            $user->id,
            ['changes' => $changes]
        );
    }

    public function logPermissionChange(User $user, string $action, string $permission, ?User $changedBy = null): UserActivityLog
    {
        $type = $action === 'granted' ? UserActivityLog::TYPE_PERMISSION_GRANTED : UserActivityLog::TYPE_PERMISSION_REVOKED;
        $changedByText = $changedBy ? " by {$changedBy->name}" : '';
        
        return UserActivityLog::log(
            $changedBy ? $changedBy->id : $user->id,
            $type,
            "Permission '{$permission}' {$action} to user {$user->name}{$changedByText}",
            User::class,
            $user->id,
            ['permission' => $permission, 'action' => $action, 'changed_by' => $changedBy?->id]
        );
    }

    public function getRecentActivities(User $user, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return UserActivityLog::forUser($user)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    public function getSecurityActivities(User $user, int $days = 30): \Illuminate\Database\Eloquent\Collection
    {
        return UserActivityLog::forUser($user)
            ->securityRelated()
            ->recent($days)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function getFailedLoginAttempts(User $user, int $days = 7): \Illuminate\Database\Eloquent\Collection
    {
        return UserActivityLog::forUser($user)
            ->ofType(UserActivityLog::TYPE_LOGIN_FAILED)
            ->recent($days)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function cleanupOldLogs(int $days = 90): int
    {
        return UserActivityLog::where('created_at', '<', now()->subDays($days))->delete();
    }
}
