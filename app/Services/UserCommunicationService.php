<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\User;
use App\Models\UserNotificationPreference;
use App\Models\UserCommunicationLog;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

final class UserCommunicationService
{
    public function __construct(
        private UserActivityLogger $activityLogger
    ) {}

    public function sendBulkMessage(
        Collection $users,
        string $subject,
        string $message,
        array $channels = ['email'],
        ?User $sentBy = null,
        array $options = []
    ): array {
        $results = [
            'total' => $users->count(),
            'sent' => 0,
            'failed' => 0,
            'skipped' => 0,
            'errors' => [],
        ];

        foreach ($users as $user) {
            try {
                $sent = $this->sendMessage($user, $subject, $message, $channels, $sentBy, $options);
                
                if ($sent) {
                    $results['sent']++;
                } else {
                    $results['skipped']++;
                }
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = "User {$user->name}: " . $e->getMessage();
            }
        }

        // Log the bulk communication
        if ($sentBy) {
            $this->activityLogger->logBulkAction(
                'bulk_message',
                'User',
                $results['sent'],
                $sentBy
            );
        }

        return $results;
    }

    public function sendMessage(
        User $user,
        string $subject,
        string $message,
        array $channels = ['email'],
        ?User $sentBy = null,
        array $options = []
    ): bool {
        $sent = false;

        foreach ($channels as $channel) {
            if ($this->canSendToChannel($user, $channel, $options['notification_type'] ?? 'system_announcements')) {
                try {
                    $this->sendToChannel($user, $channel, $subject, $message, $options);
                    $sent = true;
                    
                    // Log the communication
                    $this->logCommunication($user, $channel, $subject, $message, 'sent', $sentBy);
                } catch (\Exception $e) {
                    $this->logCommunication($user, $channel, $subject, $message, 'failed', $sentBy, $e->getMessage());
                    throw $e;
                }
            } else {
                $this->logCommunication($user, $channel, $subject, $message, 'skipped', $sentBy, 'User preferences or quiet hours');
            }
        }

        return $sent;
    }

    public function sendWelcomeMessage(User $user): bool
    {
        $subject = 'Welcome to ' . config('app.name');
        $message = $this->getWelcomeMessageTemplate($user);
        
        return $this->sendMessage($user, $subject, $message, ['email'], null, [
            'notification_type' => 'system_announcements',
            'template' => 'welcome',
        ]);
    }

    public function sendPasswordResetNotification(User $user, string $token): bool
    {
        $subject = 'Password Reset Request';
        $message = $this->getPasswordResetTemplate($user, $token);
        
        return $this->sendMessage($user, $subject, $message, ['email'], null, [
            'notification_type' => 'password_changes',
            'template' => 'password_reset',
            'token' => $token,
        ]);
    }

    public function sendAccountActivationNotification(User $user): bool
    {
        $subject = 'Account Activated';
        $message = $this->getAccountActivationTemplate($user);
        
        return $this->sendMessage($user, $subject, $message, ['email', 'in_app'], null, [
            'notification_type' => 'account_changes',
            'template' => 'account_activation',
        ]);
    }

    public function sendMaintenanceNotification(Collection $users, Carbon $startTime, Carbon $endTime, string $description): array
    {
        $subject = 'Scheduled Maintenance Notification';
        $message = $this->getMaintenanceTemplate($startTime, $endTime, $description);
        
        return $this->sendBulkMessage($users, $subject, $message, ['email', 'in_app'], null, [
            'notification_type' => 'maintenance_notices',
            'template' => 'maintenance',
        ]);
    }

    public function sendRoleChangeNotification(User $user, string $action, string $roleName, ?User $changedBy = null): bool
    {
        $subject = 'Role Assignment Update';
        $message = $this->getRoleChangeTemplate($user, $action, $roleName, $changedBy);
        
        return $this->sendMessage($user, $subject, $message, ['email', 'in_app'], $changedBy, [
            'notification_type' => 'role_changes',
            'template' => 'role_change',
        ]);
    }

    public function sendDataExportNotification(User $user, string $exportType, string $filename): bool
    {
        $subject = 'Data Export Complete';
        $message = $this->getDataExportTemplate($user, $exportType, $filename);
        
        return $this->sendMessage($user, $subject, $message, ['email', 'in_app'], null, [
            'notification_type' => 'data_exports',
            'template' => 'data_export',
        ]);
    }

    public function sendDigestNotifications(): array
    {
        $results = [
            'hourly' => 0,
            'daily' => 0,
            'weekly' => 0,
            'monthly' => 0,
        ];

        $now = now();

        // Send hourly digests
        if ($now->minute === 0) {
            $results['hourly'] = $this->sendDigest('hourly');
        }

        // Send daily digests at 9 AM
        if ($now->hour === 9 && $now->minute === 0) {
            $results['daily'] = $this->sendDigest('daily');
        }

        // Send weekly digests on Monday at 9 AM
        if ($now->isMonday() && $now->hour === 9 && $now->minute === 0) {
            $results['weekly'] = $this->sendDigest('weekly');
        }

        // Send monthly digests on the 1st at 9 AM
        if ($now->day === 1 && $now->hour === 9 && $now->minute === 0) {
            $results['monthly'] = $this->sendDigest('monthly');
        }

        return $results;
    }

    private function canSendToChannel(User $user, string $channel, string $notificationType): bool
    {
        $preference = UserNotificationPreference::forUser($user)
            ->forType($notificationType)
            ->forChannel($channel)
            ->first();

        if (!$preference) {
            // If no preference exists, check if it's a critical notification
            $criticalTypes = [
                'security_alerts',
                'password_changes',
                'account_changes',
            ];
            
            return in_array($notificationType, $criticalTypes);
        }

        return $preference->shouldSendNotification();
    }

    private function sendToChannel(User $user, string $channel, string $subject, string $message, array $options): void
    {
        switch ($channel) {
            case 'email':
                $this->sendEmail($user, $subject, $message, $options);
                break;
            case 'sms':
                $this->sendSms($user, $subject, $message, $options);
                break;
            case 'push':
                $this->sendPushNotification($user, $subject, $message, $options);
                break;
            case 'in_app':
                $this->sendInAppNotification($user, $subject, $message, $options);
                break;
            case 'desktop':
                $this->sendDesktopNotification($user, $subject, $message, $options);
                break;
            default:
                throw new \InvalidArgumentException("Unsupported channel: {$channel}");
        }
    }

    private function sendEmail(User $user, string $subject, string $message, array $options): void
    {
        // In a real application, you would use a proper Mailable class
        Log::info('Email sent', [
            'to' => $user->email,
            'subject' => $subject,
            'template' => $options['template'] ?? 'default',
        ]);

        // TODO: Implement actual email sending
        // Mail::to($user->email)->send(new UserNotificationMail($subject, $message, $options));
    }

    private function sendSms(User $user, string $subject, string $message, array $options): void
    {
        if (!$user->phone) {
            throw new \Exception('User has no phone number');
        }

        Log::info('SMS sent', [
            'to' => $user->phone,
            'subject' => $subject,
        ]);

        // TODO: Implement SMS sending (Twilio, etc.)
    }

    private function sendPushNotification(User $user, string $subject, string $message, array $options): void
    {
        Log::info('Push notification sent', [
            'user_id' => $user->id,
            'subject' => $subject,
        ]);

        // TODO: Implement push notifications (Firebase, etc.)
    }

    private function sendInAppNotification(User $user, string $subject, string $message, array $options): void
    {
        \Filament\Notifications\Notification::make()
            ->title($subject)
            ->body($message)
            ->icon($this->getNotificationIcon($options['notification_type'] ?? 'system_announcements'))
            ->color($this->getNotificationColor($options['notification_type'] ?? 'system_announcements'))
            ->sendToDatabase($user);
    }

    private function sendDesktopNotification(User $user, string $subject, string $message, array $options): void
    {
        Log::info('Desktop notification sent', [
            'user_id' => $user->id,
            'subject' => $subject,
        ]);

        // TODO: Implement desktop notifications
    }

    private function sendDigest(string $frequency): int
    {
        // TODO: Implement digest functionality
        Log::info("Sending {$frequency} digest notifications");
        return 0;
    }

    private function logCommunication(
        User $user,
        string $channel,
        string $subject,
        string $message,
        string $status,
        ?User $sentBy = null,
        ?string $error = null
    ): void {
        // TODO: Create UserCommunicationLog model and log the communication
        Log::info('Communication logged', [
            'user_id' => $user->id,
            'channel' => $channel,
            'subject' => $subject,
            'status' => $status,
            'sent_by' => $sentBy?->id,
            'error' => $error,
        ]);
    }

    private function getNotificationIcon(string $type): string
    {
        return match ($type) {
            'security_alerts' => 'heroicon-o-shield-exclamation',
            'password_changes' => 'heroicon-o-key',
            'account_changes' => 'heroicon-o-user-circle',
            'role_changes' => 'heroicon-o-shield-check',
            'maintenance_notices' => 'heroicon-o-wrench-screwdriver',
            'data_exports' => 'heroicon-o-arrow-down-tray',
            default => 'heroicon-o-bell',
        };
    }

    private function getNotificationColor(string $type): string
    {
        return match ($type) {
            'security_alerts' => 'danger',
            'password_changes' => 'warning',
            'account_changes' => 'info',
            'role_changes' => 'success',
            'maintenance_notices' => 'warning',
            'data_exports' => 'info',
            default => 'gray',
        };
    }

    private function getWelcomeMessageTemplate(User $user): string
    {
        return "Welcome to " . config('app.name') . ", {$user->name}!\n\n" .
               "Your account has been created successfully. You can now access the system and explore all available features.\n\n" .
               "If you have any questions, please don't hesitate to contact our support team.";
    }

    private function getPasswordResetTemplate(User $user, string $token): string
    {
        return "Hello {$user->name},\n\n" .
               "You have requested a password reset for your account. Please use the following link to reset your password:\n\n" .
               "Reset Link: [Password Reset URL with token: {$token}]\n\n" .
               "If you did not request this reset, please ignore this email.";
    }

    private function getAccountActivationTemplate(User $user): string
    {
        return "Hello {$user->name},\n\n" .
               "Your account has been activated and you now have full access to the system.\n\n" .
               "You can log in and start using all available features.";
    }

    private function getMaintenanceTemplate(Carbon $startTime, Carbon $endTime, string $description): string
    {
        return "Scheduled Maintenance Notice\n\n" .
               "We will be performing scheduled maintenance on our system:\n\n" .
               "Start Time: {$startTime->format('Y-m-d H:i:s T')}\n" .
               "End Time: {$endTime->format('Y-m-d H:i:s T')}\n" .
               "Duration: {$startTime->diffForHumans($endTime, true)}\n\n" .
               "Description: {$description}\n\n" .
               "During this time, the system may be unavailable. We apologize for any inconvenience.";
    }

    private function getRoleChangeTemplate(User $user, string $action, string $roleName, ?User $changedBy): string
    {
        $changedByText = $changedBy ? " by {$changedBy->name}" : '';
        
        return "Hello {$user->name},\n\n" .
               "Your role has been updated. The role '{$roleName}' has been {$action}{$changedByText}.\n\n" .
               "This change affects your access permissions within the system.";
    }

    private function getDataExportTemplate(User $user, string $exportType, string $filename): string
    {
        return "Hello {$user->name},\n\n" .
               "Your {$exportType} data export has been completed successfully.\n\n" .
               "File: {$filename}\n\n" .
               "You can download the file from your account dashboard.";
    }
}
