<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

final class PasswordPolicyService
{
    private array $config;

    public function __construct()
    {
        $this->config = config('auth.password_policy', [
            'min_length' => 8,
            'require_uppercase' => true,
            'require_lowercase' => true,
            'require_numbers' => true,
            'require_symbols' => true,
            'max_age_days' => 90,
            'history_count' => 5,
            'lockout_attempts' => 5,
            'lockout_duration_minutes' => 30,
            'complexity_score_required' => 3,
        ]);
    }

    public function getPasswordRules(): Password
    {
        $rules = Password::min($this->config['min_length']);

        if ($this->config['require_uppercase']) {
            $rules->mixedCase();
        }

        if ($this->config['require_numbers']) {
            $rules->numbers();
        }

        if ($this->config['require_symbols']) {
            $rules->symbols();
        }

        return $rules;
    }

    public function validatePassword(string $password, ?User $user = null): array
    {
        $errors = [];
        $score = 0;

        // Length check
        if (strlen($password) < $this->config['min_length']) {
            $errors[] = "Password must be at least {$this->config['min_length']} characters long";
        } else {
            $score++;
        }

        // Uppercase check
        if ($this->config['require_uppercase'] && !preg_match('/[A-Z]/', $password)) {
            $errors[] = 'Password must contain at least one uppercase letter';
        } else {
            $score++;
        }

        // Lowercase check
        if ($this->config['require_lowercase'] && !preg_match('/[a-z]/', $password)) {
            $errors[] = 'Password must contain at least one lowercase letter';
        } else {
            $score++;
        }

        // Numbers check
        if ($this->config['require_numbers'] && !preg_match('/[0-9]/', $password)) {
            $errors[] = 'Password must contain at least one number';
        } else {
            $score++;
        }

        // Symbols check
        if ($this->config['require_symbols'] && !preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'Password must contain at least one special character';
        } else {
            $score++;
        }

        // Common password check
        if ($this->isCommonPassword($password)) {
            $errors[] = 'Password is too common. Please choose a more unique password';
        }

        // User-specific checks
        if ($user) {
            // Check against user information
            if ($this->containsUserInfo($password, $user)) {
                $errors[] = 'Password cannot contain your personal information';
            }

            // Check password history
            if ($this->isInPasswordHistory($password, $user)) {
                $errors[] = "Password cannot be one of your last {$this->config['history_count']} passwords";
            }
        }

        // Complexity score check
        if ($score < $this->config['complexity_score_required']) {
            $errors[] = 'Password does not meet complexity requirements';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'score' => $score,
            'strength' => $this->getPasswordStrength($score),
        ];
    }

    public function getPasswordStrength(int $score): string
    {
        return match (true) {
            $score >= 5 => 'Very Strong',
            $score >= 4 => 'Strong',
            $score >= 3 => 'Medium',
            $score >= 2 => 'Weak',
            default => 'Very Weak',
        };
    }

    public function getPasswordStrengthColor(int $score): string
    {
        return match (true) {
            $score >= 5 => 'success',
            $score >= 4 => 'info',
            $score >= 3 => 'warning',
            $score >= 2 => 'danger',
            default => 'gray',
        };
    }

    public function isPasswordExpired(User $user): bool
    {
        if (!$user->password_changed_at) {
            return true; // Force password change if never set
        }

        return $user->password_changed_at->diffInDays(now()) > $this->config['max_age_days'];
    }

    public function getDaysUntilExpiration(User $user): int
    {
        if (!$user->password_changed_at) {
            return 0;
        }

        $daysSinceChange = $user->password_changed_at->diffInDays(now());
        return max(0, $this->config['max_age_days'] - $daysSinceChange);
    }

    public function shouldLockAccount(User $user): bool
    {
        return $user->login_attempts >= $this->config['lockout_attempts'];
    }

    public function getLockoutDuration(): int
    {
        return $this->config['lockout_duration_minutes'];
    }

    public function generateSecurePassword(int $length = 12): string
    {
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $numbers = '**********';
        $symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

        $password = '';
        
        // Ensure at least one character from each required set
        if ($this->config['require_uppercase']) {
            $password .= $uppercase[random_int(0, strlen($uppercase) - 1)];
        }
        if ($this->config['require_lowercase']) {
            $password .= $lowercase[random_int(0, strlen($lowercase) - 1)];
        }
        if ($this->config['require_numbers']) {
            $password .= $numbers[random_int(0, strlen($numbers) - 1)];
        }
        if ($this->config['require_symbols']) {
            $password .= $symbols[random_int(0, strlen($symbols) - 1)];
        }

        // Fill the rest with random characters
        $allChars = $lowercase . $uppercase . $numbers . $symbols;
        for ($i = strlen($password); $i < $length; $i++) {
            $password .= $allChars[random_int(0, strlen($allChars) - 1)];
        }

        // Shuffle the password
        return str_shuffle($password);
    }

    private function isCommonPassword(string $password): bool
    {
        $commonPasswords = [
            'password', '123456', '123456789', 'qwerty', 'abc123',
            'password123', 'admin', 'letmein', 'welcome', 'monkey',
            'dragon', 'master', 'shadow', 'superman', 'michael',
            'football', 'baseball', 'liverpool', 'jordan', 'princess',
        ];

        return in_array(strtolower($password), $commonPasswords);
    }

    private function containsUserInfo(string $password, User $user): bool
    {
        $userInfo = [
            strtolower($user->name),
            strtolower($user->email),
            strtolower(explode('@', $user->email)[0]),
        ];

        if ($user->phone) {
            $userInfo[] = preg_replace('/\D/', '', $user->phone);
        }

        if ($user->date_of_birth) {
            $userInfo[] = $user->date_of_birth->format('Y');
            $userInfo[] = $user->date_of_birth->format('m');
            $userInfo[] = $user->date_of_birth->format('d');
            $userInfo[] = $user->date_of_birth->format('md');
            $userInfo[] = $user->date_of_birth->format('dm');
        }

        $passwordLower = strtolower($password);

        foreach ($userInfo as $info) {
            if ($info && strlen($info) >= 3 && str_contains($passwordLower, $info)) {
                return true;
            }
        }

        return false;
    }

    private function isInPasswordHistory(string $password, User $user): bool
    {
        // This would require a password history table
        // For now, just check against current password
        return Hash::check($password, $user->password);
    }

    public function getPasswordPolicy(): array
    {
        return [
            'min_length' => $this->config['min_length'],
            'require_uppercase' => $this->config['require_uppercase'],
            'require_lowercase' => $this->config['require_lowercase'],
            'require_numbers' => $this->config['require_numbers'],
            'require_symbols' => $this->config['require_symbols'],
            'max_age_days' => $this->config['max_age_days'],
            'history_count' => $this->config['history_count'],
            'lockout_attempts' => $this->config['lockout_attempts'],
            'lockout_duration_minutes' => $this->config['lockout_duration_minutes'],
        ];
    }

    public function getPasswordPolicyDescription(): string
    {
        $requirements = [];

        $requirements[] = "At least {$this->config['min_length']} characters long";

        if ($this->config['require_uppercase']) {
            $requirements[] = "At least one uppercase letter";
        }

        if ($this->config['require_lowercase']) {
            $requirements[] = "At least one lowercase letter";
        }

        if ($this->config['require_numbers']) {
            $requirements[] = "At least one number";
        }

        if ($this->config['require_symbols']) {
            $requirements[] = "At least one special character";
        }

        $requirements[] = "Cannot be a common password";
        $requirements[] = "Cannot contain your personal information";
        $requirements[] = "Cannot be one of your recent passwords";

        return "Password must meet the following requirements:\n• " . implode("\n• ", $requirements);
    }
}
