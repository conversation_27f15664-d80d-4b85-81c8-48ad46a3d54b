<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\User;
use App\Models\UserSession;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Filament\Notifications\Notification as FilamentNotification;

final class SecurityNotificationService
{
    public function notifySuccessfulLogin(User $user, UserSession $session): void
    {
        if (!$user->getNotificationPreference('email_login_alerts')) {
            return;
        }

        $deviceInfo = $session->device_info;
        $location = $session->location ?: 'Unknown location';

        // Send email notification
        $this->sendSecurityEmail($user, [
            'subject' => 'Successful Login to Your Account',
            'title' => 'New Login Detected',
            'message' => "We detected a new login to your account.",
            'details' => [
                'Time' => $session->login_at->format('M j, Y \a\t g:i A T'),
                'IP Address' => $session->ip_address,
                'Location' => $location,
                'Device' => $deviceInfo['device'],
                'Browser' => $deviceInfo['browser'],
                'Operating System' => $deviceInfo['os'],
            ],
            'action_text' => 'Review Account Activity',
            'action_url' => route('filament.admin.resources.user-sessions.index'),
            'security_tip' => 'If this wasn\'t you, please change your password immediately and contact support.',
        ]);

        // Send in-app notification
        FilamentNotification::make()
            ->title('New Login Detected')
            ->body("Login from {$deviceInfo['device']} at {$location}")
            ->icon('heroicon-o-shield-check')
            ->color('info')
            ->sendToDatabase($user);
    }

    public function notifyFailedLogin(User $user, string $ipAddress, string $userAgent): void
    {
        if (!$user->getNotificationPreference('email_login_alerts')) {
            return;
        }

        $deviceInfo = app(SessionManagementService::class)->getDeviceInfo($userAgent);

        // Send email notification
        $this->sendSecurityEmail($user, [
            'subject' => 'Failed Login Attempt on Your Account',
            'title' => 'Failed Login Attempt',
            'message' => "Someone tried to log into your account with an incorrect password.",
            'details' => [
                'Time' => now()->format('M j, Y \a\t g:i A T'),
                'IP Address' => $ipAddress,
                'Device' => $deviceInfo['device'],
                'Browser' => $deviceInfo['browser'],
                'Operating System' => $deviceInfo['os'],
            ],
            'action_text' => 'Secure Your Account',
            'action_url' => route('filament.admin.auth.password-reset.request'),
            'security_tip' => 'If this wasn\'t you, consider changing your password and enabling two-factor authentication.',
        ]);

        // Send in-app notification
        FilamentNotification::make()
            ->title('Failed Login Attempt')
            ->body("Failed login attempt from {$ipAddress}")
            ->icon('heroicon-o-exclamation-triangle')
            ->color('warning')
            ->sendToDatabase($user);
    }

    public function notifyAccountLocked(User $user): void
    {
        // Send email notification
        $this->sendSecurityEmail($user, [
            'subject' => 'Your Account Has Been Locked',
            'title' => 'Account Locked',
            'message' => "Your account has been temporarily locked due to multiple failed login attempts.",
            'details' => [
                'Time' => now()->format('M j, Y \a\t g:i A T'),
                'Reason' => 'Multiple failed login attempts',
                'Duration' => '30 minutes',
            ],
            'action_text' => 'Reset Password',
            'action_url' => route('filament.admin.auth.password-reset.request'),
            'security_tip' => 'Your account will be automatically unlocked after 30 minutes, or you can reset your password now.',
        ]);

        // Send in-app notification
        FilamentNotification::make()
            ->title('Account Locked')
            ->body('Your account has been locked due to multiple failed login attempts')
            ->icon('heroicon-o-lock-closed')
            ->color('danger')
            ->sendToDatabase($user);
    }

    public function notifyPasswordChanged(User $user): void
    {
        if (!$user->getNotificationPreference('email_password_changes')) {
            return;
        }

        // Send email notification
        $this->sendSecurityEmail($user, [
            'subject' => 'Your Password Has Been Changed',
            'title' => 'Password Changed',
            'message' => "Your account password has been successfully changed.",
            'details' => [
                'Time' => now()->format('M j, Y \a\t g:i A T'),
                'IP Address' => request()->ip(),
            ],
            'action_text' => 'Review Account Activity',
            'action_url' => route('filament.admin.resources.user-activity-logs.index'),
            'security_tip' => 'If you didn\'t make this change, please contact support immediately.',
        ]);

        // Send in-app notification
        FilamentNotification::make()
            ->title('Password Changed')
            ->body('Your password has been successfully updated')
            ->icon('heroicon-o-key')
            ->color('success')
            ->sendToDatabase($user);
    }

    public function notifyPasswordExpiring(User $user, int $daysUntilExpiration): void
    {
        if (!$user->getNotificationPreference('email_password_changes')) {
            return;
        }

        $urgency = $daysUntilExpiration <= 3 ? 'urgent' : 'normal';
        $color = $daysUntilExpiration <= 3 ? 'danger' : 'warning';

        // Send email notification
        $this->sendSecurityEmail($user, [
            'subject' => 'Your Password Will Expire Soon',
            'title' => 'Password Expiring Soon',
            'message' => "Your password will expire in {$daysUntilExpiration} day" . ($daysUntilExpiration !== 1 ? 's' : '') . ".",
            'details' => [
                'Days Remaining' => $daysUntilExpiration,
                'Expiration Date' => now()->addDays($daysUntilExpiration)->format('M j, Y'),
            ],
            'action_text' => 'Change Password Now',
            'action_url' => route('filament.admin.auth.password-reset.request'),
            'security_tip' => 'Regular password changes help keep your account secure.',
        ]);

        // Send in-app notification
        FilamentNotification::make()
            ->title('Password Expiring Soon')
            ->body("Your password expires in {$daysUntilExpiration} day" . ($daysUntilExpiration !== 1 ? 's' : ''))
            ->icon('heroicon-o-clock')
            ->color($color)
            ->sendToDatabase($user);
    }

    public function notifySuspiciousActivity(User $user, string $activity, array $details = []): void
    {
        // Send email notification
        $this->sendSecurityEmail($user, [
            'subject' => 'Suspicious Activity Detected',
            'title' => 'Security Alert',
            'message' => "We detected suspicious activity on your account: {$activity}",
            'details' => array_merge([
                'Time' => now()->format('M j, Y \a\t g:i A T'),
                'Activity' => $activity,
            ], $details),
            'action_text' => 'Review Account Security',
            'action_url' => route('filament.admin.resources.user-sessions.index'),
            'security_tip' => 'If this activity wasn\'t authorized by you, please change your password and contact support immediately.',
        ]);

        // Send in-app notification
        FilamentNotification::make()
            ->title('Suspicious Activity Detected')
            ->body($activity)
            ->icon('heroicon-o-shield-exclamation')
            ->color('danger')
            ->sendToDatabase($user);
    }

    public function notifyNewDeviceLogin(User $user, UserSession $session): void
    {
        if (!$user->getNotificationPreference('email_login_alerts')) {
            return;
        }

        $deviceInfo = $session->device_info;
        $location = $session->location ?: 'Unknown location';

        // Send email notification
        $this->sendSecurityEmail($user, [
            'subject' => 'New Device Login Detected',
            'title' => 'New Device Login',
            'message' => "We detected a login from a new device.",
            'details' => [
                'Time' => $session->login_at->format('M j, Y \a\t g:i A T'),
                'IP Address' => $session->ip_address,
                'Location' => $location,
                'Device' => $deviceInfo['device'],
                'Browser' => $deviceInfo['browser'],
                'Operating System' => $deviceInfo['os'],
            ],
            'action_text' => 'Review Active Sessions',
            'action_url' => route('filament.admin.resources.user-sessions.index'),
            'security_tip' => 'If this wasn\'t you, please end all sessions and change your password immediately.',
        ]);

        // Send in-app notification
        FilamentNotification::make()
            ->title('New Device Login')
            ->body("Login from new {$deviceInfo['device']} device")
            ->icon('heroicon-o-device-phone-mobile')
            ->color('warning')
            ->sendToDatabase($user);
    }

    public function notifyTwoFactorEnabled(User $user): void
    {
        // Send email notification
        $this->sendSecurityEmail($user, [
            'subject' => 'Two-Factor Authentication Enabled',
            'title' => '2FA Enabled',
            'message' => "Two-factor authentication has been enabled on your account.",
            'details' => [
                'Time' => now()->format('M j, Y \a\t g:i A T'),
                'IP Address' => request()->ip(),
            ],
            'action_text' => 'Manage Security Settings',
            'action_url' => route('filament.admin.pages.profile'),
            'security_tip' => 'Your account is now more secure with two-factor authentication enabled.',
        ]);

        // Send in-app notification
        FilamentNotification::make()
            ->title('Two-Factor Authentication Enabled')
            ->body('Your account security has been enhanced')
            ->icon('heroicon-o-shield-check')
            ->color('success')
            ->sendToDatabase($user);
    }

    public function notifyTwoFactorDisabled(User $user): void
    {
        // Send email notification
        $this->sendSecurityEmail($user, [
            'subject' => 'Two-Factor Authentication Disabled',
            'title' => '2FA Disabled',
            'message' => "Two-factor authentication has been disabled on your account.",
            'details' => [
                'Time' => now()->format('M j, Y \a\t g:i A T'),
                'IP Address' => request()->ip(),
            ],
            'action_text' => 'Re-enable 2FA',
            'action_url' => route('filament.admin.pages.profile'),
            'security_tip' => 'Consider re-enabling two-factor authentication to keep your account secure.',
        ]);

        // Send in-app notification
        FilamentNotification::make()
            ->title('Two-Factor Authentication Disabled')
            ->body('Your account security level has been reduced')
            ->icon('heroicon-o-shield-exclamation')
            ->color('warning')
            ->sendToDatabase($user);
    }

    private function sendSecurityEmail(User $user, array $data): void
    {
        // In a real application, you would create a proper Mailable class
        // For now, we'll just log the notification
        \Log::info('Security notification sent', [
            'user_id' => $user->id,
            'email' => $user->email,
            'subject' => $data['subject'],
            'title' => $data['title'],
        ]);

        // TODO: Implement actual email sending
        // Mail::to($user->email)->send(new SecurityNotificationMail($data));
    }

    public function checkAndNotifySecurityIssues(User $user): void
    {
        $passwordPolicy = app(PasswordPolicyService::class);

        // Check for password expiration
        if ($passwordPolicy->isPasswordExpired($user)) {
            $this->notifyPasswordExpiring($user, 0);
        } else {
            $daysUntilExpiration = $passwordPolicy->getDaysUntilExpiration($user);
            if ($daysUntilExpiration <= 7) {
                $this->notifyPasswordExpiring($user, $daysUntilExpiration);
            }
        }

        // Check for suspicious sessions
        $sessionService = app(SessionManagementService::class);
        $suspiciousSessions = $sessionService->detectConcurrentSessions($user);
        
        if (!empty($suspiciousSessions)) {
            $this->notifySuspiciousActivity($user, 'Multiple concurrent sessions detected', [
                'Session Count' => count($suspiciousSessions),
            ]);
        }
    }
}
