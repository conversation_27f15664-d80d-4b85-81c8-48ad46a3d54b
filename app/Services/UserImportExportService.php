<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\User;
use App\Models\UserActivityLog;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Collection;
use Illuminate\Http\UploadedFile;
use Spatie\Permission\Models\Role;
use Carbon\Carbon;

final class UserImportExportService
{
    private UserActivityLogger $activityLogger;
    private PasswordPolicyService $passwordPolicy;

    public function __construct(
        UserActivityLogger $activityLogger,
        PasswordPolicyService $passwordPolicy
    ) {
        $this->activityLogger = $activityLogger;
        $this->passwordPolicy = $passwordPolicy;
    }

    public function importFromCsv(UploadedFile $file, ?User $importedBy = null): array
    {
        $results = [
            'total' => 0,
            'imported' => 0,
            'updated' => 0,
            'errors' => [],
            'warnings' => [],
        ];

        try {
            $csvData = $this->parseCsvFile($file);
            $results['total'] = count($csvData);

            foreach ($csvData as $index => $row) {
                $rowNumber = $index + 2; // +2 because index starts at 0 and we skip header
                
                try {
                    $result = $this->importUserRow($row, $importedBy);
                    
                    if ($result['action'] === 'created') {
                        $results['imported']++;
                    } elseif ($result['action'] === 'updated') {
                        $results['updated']++;
                    }

                    if (!empty($result['warnings'])) {
                        $results['warnings'][] = "Row {$rowNumber}: " . implode(', ', $result['warnings']);
                    }

                } catch (\Exception $e) {
                    $results['errors'][] = "Row {$rowNumber}: " . $e->getMessage();
                }
            }

            // Log the import activity
            if ($importedBy) {
                $this->activityLogger->logDataImport('users', $results['imported'] + $results['updated'], $importedBy);
            }

        } catch (\Exception $e) {
            $results['errors'][] = "File processing error: " . $e->getMessage();
        }

        return $results;
    }

    public function exportToCsv(array $userIds = [], array $columns = []): string
    {
        $defaultColumns = [
            'id', 'name', 'email', 'phone', 'employee_id', 'department', 'position',
            'account_status', 'created_at', 'last_login_at', 'roles'
        ];

        $columns = empty($columns) ? $defaultColumns : $columns;
        
        $query = User::query();
        
        if (!empty($userIds)) {
            $query->whereIn('id', $userIds);
        }

        $users = $query->with('roles')->get();

        $csvData = [];
        
        // Add header row
        $csvData[] = $this->formatCsvHeaders($columns);

        // Add data rows
        foreach ($users as $user) {
            $row = [];
            foreach ($columns as $column) {
                $row[] = $this->formatCsvValue($user, $column);
            }
            $csvData[] = $row;
        }

        return $this->arrayToCsv($csvData);
    }

    public function exportUserTemplate(): string
    {
        $headers = [
            'name', 'email', 'phone', 'employee_id', 'department', 'position',
            'date_of_birth', 'gender', 'address', 'city', 'state', 'country',
            'roles', 'account_status', 'must_change_password'
        ];

        $sampleData = [
            'John Doe', '<EMAIL>', '+**********', 'EMP001', 'IT',
            'Software Developer', '1990-01-15', 'male', '123 Main St',
            'New York', 'NY', 'USA', 'panel_user', 'active', 'true'
        ];

        $csvData = [$headers, $sampleData];
        return $this->arrayToCsv($csvData);
    }

    public function validateImportData(array $data): array
    {
        $errors = [];
        $warnings = [];

        foreach ($data as $index => $row) {
            $rowNumber = $index + 2;
            $rowErrors = [];
            $rowWarnings = [];

            // Validate required fields
            if (empty($row['name'])) {
                $rowErrors[] = 'Name is required';
            }

            if (empty($row['email'])) {
                $rowErrors[] = 'Email is required';
            } elseif (!filter_var($row['email'], FILTER_VALIDATE_EMAIL)) {
                $rowErrors[] = 'Invalid email format';
            }

            // Check for duplicate email
            if (!empty($row['email'])) {
                $existingUser = User::where('email', $row['email'])->first();
                if ($existingUser) {
                    $rowWarnings[] = 'Email already exists - user will be updated';
                }
            }

            // Validate employee ID uniqueness
            if (!empty($row['employee_id'])) {
                $existingUser = User::where('employee_id', $row['employee_id'])->first();
                if ($existingUser && $existingUser->email !== $row['email']) {
                    $rowErrors[] = 'Employee ID already exists for different user';
                }
            }

            // Validate roles
            if (!empty($row['roles'])) {
                $roles = explode(',', $row['roles']);
                foreach ($roles as $roleName) {
                    $roleName = trim($roleName);
                    if (!Role::where('name', $roleName)->exists()) {
                        $rowWarnings[] = "Role '{$roleName}' does not exist";
                    }
                }
            }

            // Validate account status
            if (!empty($row['account_status'])) {
                $validStatuses = ['active', 'inactive', 'suspended', 'pending', 'locked'];
                if (!in_array($row['account_status'], $validStatuses)) {
                    $rowErrors[] = 'Invalid account status';
                }
            }

            // Validate date of birth
            if (!empty($row['date_of_birth'])) {
                try {
                    $date = Carbon::parse($row['date_of_birth']);
                    if ($date->isFuture()) {
                        $rowErrors[] = 'Date of birth cannot be in the future';
                    }
                } catch (\Exception $e) {
                    $rowErrors[] = 'Invalid date of birth format';
                }
            }

            if (!empty($rowErrors)) {
                $errors["Row {$rowNumber}"] = $rowErrors;
            }

            if (!empty($rowWarnings)) {
                $warnings["Row {$rowNumber}"] = $rowWarnings;
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings,
        ];
    }

    private function parseCsvFile(UploadedFile $file): array
    {
        $content = file_get_contents($file->getPathname());
        $lines = explode("\n", $content);
        
        if (empty($lines)) {
            throw new \Exception('CSV file is empty');
        }

        $headers = str_getcsv(array_shift($lines));
        $data = [];

        foreach ($lines as $line) {
            if (trim($line) === '') {
                continue;
            }

            $row = str_getcsv($line);
            if (count($row) !== count($headers)) {
                continue; // Skip malformed rows
            }

            $data[] = array_combine($headers, $row);
        }

        return $data;
    }

    private function importUserRow(array $row, ?User $importedBy = null): array
    {
        $warnings = [];
        $action = 'created';

        // Check if user exists
        $user = User::where('email', $row['email'])->first();
        
        if ($user) {
            $action = 'updated';
        } else {
            $user = new User();
            // Generate a temporary password for new users
            $tempPassword = $this->passwordPolicy->generateSecurePassword();
            $user->password = Hash::make($tempPassword);
            $user->must_change_password = true;
        }

        // Map basic fields
        $user->name = $row['name'];
        $user->email = $row['email'];
        
        if (!empty($row['phone'])) {
            $user->phone = $row['phone'];
        }

        if (!empty($row['employee_id'])) {
            $user->employee_id = $row['employee_id'];
        }

        if (!empty($row['department'])) {
            $user->department = $row['department'];
        }

        if (!empty($row['position'])) {
            $user->position = $row['position'];
        }

        if (!empty($row['date_of_birth'])) {
            try {
                $user->date_of_birth = Carbon::parse($row['date_of_birth']);
            } catch (\Exception $e) {
                $warnings[] = 'Invalid date of birth format';
            }
        }

        if (!empty($row['gender'])) {
            $validGenders = ['male', 'female', 'other', 'prefer_not_to_say'];
            if (in_array($row['gender'], $validGenders)) {
                $user->gender = $row['gender'];
            } else {
                $warnings[] = 'Invalid gender value';
            }
        }

        if (!empty($row['address'])) {
            $user->address = $row['address'];
        }

        if (!empty($row['city'])) {
            $user->city = $row['city'];
        }

        if (!empty($row['state'])) {
            $user->state = $row['state'];
        }

        if (!empty($row['country'])) {
            $user->country = $row['country'];
        }

        if (!empty($row['account_status'])) {
            $validStatuses = ['active', 'inactive', 'suspended', 'pending', 'locked'];
            if (in_array($row['account_status'], $validStatuses)) {
                $user->account_status = $row['account_status'];
            } else {
                $warnings[] = 'Invalid account status, using default';
                $user->account_status = 'pending';
            }
        } else {
            $user->account_status = 'pending';
        }

        if (isset($row['must_change_password'])) {
            $user->must_change_password = filter_var($row['must_change_password'], FILTER_VALIDATE_BOOLEAN);
        }

        $user->save();

        // Assign roles
        if (!empty($row['roles'])) {
            $roleNames = array_map('trim', explode(',', $row['roles']));
            $validRoles = Role::whereIn('name', $roleNames)->pluck('name')->toArray();
            
            if (count($validRoles) !== count($roleNames)) {
                $invalidRoles = array_diff($roleNames, $validRoles);
                $warnings[] = 'Some roles do not exist: ' . implode(', ', $invalidRoles);
            }

            if (!empty($validRoles)) {
                $user->syncRoles($validRoles);
            }
        }

        return [
            'action' => $action,
            'user' => $user,
            'warnings' => $warnings,
        ];
    }

    private function formatCsvHeaders(array $columns): array
    {
        $headerMap = [
            'id' => 'ID',
            'name' => 'Name',
            'email' => 'Email',
            'phone' => 'Phone',
            'employee_id' => 'Employee ID',
            'department' => 'Department',
            'position' => 'Position',
            'account_status' => 'Account Status',
            'created_at' => 'Created At',
            'last_login_at' => 'Last Login',
            'roles' => 'Roles',
        ];

        return array_map(fn($col) => $headerMap[$col] ?? ucfirst(str_replace('_', ' ', $col)), $columns);
    }

    private function formatCsvValue(User $user, string $column): string
    {
        return match ($column) {
            'roles' => $user->roles->pluck('name')->join(', '),
            'created_at', 'last_login_at' => $user->$column?->format('Y-m-d H:i:s') ?? '',
            'date_of_birth' => $user->$column?->format('Y-m-d') ?? '',
            default => (string) ($user->$column ?? ''),
        };
    }

    private function arrayToCsv(array $data): string
    {
        $output = fopen('php://temp', 'r+');
        
        foreach ($data as $row) {
            fputcsv($output, $row);
        }
        
        rewind($output);
        $csv = stream_get_contents($output);
        fclose($output);
        
        return $csv;
    }

    public function getBulkOperationOptions(): array
    {
        return [
            'activate' => 'Activate Users',
            'deactivate' => 'Deactivate Users',
            'suspend' => 'Suspend Users',
            'force_password_change' => 'Force Password Change',
            'assign_role' => 'Assign Role',
            'remove_role' => 'Remove Role',
            'delete' => 'Delete Users',
        ];
    }

    public function performBulkOperation(string $operation, array $userIds, array $options = [], ?User $performedBy = null): array
    {
        $users = User::whereIn('id', $userIds)->get();
        $results = [
            'success' => 0,
            'errors' => [],
        ];

        foreach ($users as $user) {
            try {
                switch ($operation) {
                    case 'activate':
                        $user->activate();
                        break;
                    case 'deactivate':
                        $user->deactivate();
                        break;
                    case 'suspend':
                        $user->suspend();
                        break;
                    case 'force_password_change':
                        $user->update(['must_change_password' => true]);
                        break;
                    case 'assign_role':
                        if (!empty($options['role'])) {
                            $user->assignRole($options['role']);
                        }
                        break;
                    case 'remove_role':
                        if (!empty($options['role'])) {
                            $user->removeRole($options['role']);
                        }
                        break;
                    case 'delete':
                        $user->delete();
                        break;
                }
                $results['success']++;
            } catch (\Exception $e) {
                $results['errors'][] = "User {$user->name}: " . $e->getMessage();
            }
        }

        // Log the bulk operation
        if ($performedBy) {
            $this->activityLogger->logBulkAction($operation, 'User', $results['success'], $performedBy);
        }

        return $results;
    }
}
