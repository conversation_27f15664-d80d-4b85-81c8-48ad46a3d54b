<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

final class UserSession extends Model
{
    protected $fillable = [
        'user_id',
        'session_id',
        'ip_address',
        'user_agent',
        'login_at',
        'logout_at',
        'last_activity',
        'is_active',
        'location_country',
        'location_region',
        'location_city',
        'device_type',
        'browser',
        'operating_system',
    ];

    protected $casts = [
        'user_id' => 'integer',
        'login_at' => 'datetime',
        'logout_at' => 'datetime',
        'last_activity' => 'datetime',
        'is_active' => 'boolean',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getDurationAttribute(): ?int
    {
        if (!$this->login_at) {
            return null;
        }

        $endTime = $this->logout_at ?? $this->last_activity ?? now();
        return $this->login_at->diffInMinutes($endTime);
    }

    public function getFormattedDurationAttribute(): string
    {
        $duration = $this->duration;
        
        if (!$duration) {
            return 'Unknown';
        }

        if ($duration < 60) {
            return $duration . ' minute' . ($duration !== 1 ? 's' : '');
        }

        $hours = floor($duration / 60);
        $minutes = $duration % 60;

        $result = $hours . ' hour' . ($hours !== 1 ? 's' : '');
        
        if ($minutes > 0) {
            $result .= ', ' . $minutes . ' minute' . ($minutes !== 1 ? 's' : '');
        }

        return $result;
    }

    public function getDeviceInfoAttribute(): array
    {
        if (!$this->user_agent) {
            return [
                'device' => 'Unknown',
                'browser' => 'Unknown',
                'os' => 'Unknown',
            ];
        }

        // Use stored values if available
        if ($this->device_type && $this->browser && $this->operating_system) {
            return [
                'device' => $this->device_type,
                'browser' => $this->browser,
                'os' => $this->operating_system,
            ];
        }

        // Parse user agent
        $device = 'Unknown';
        $browser = 'Unknown';
        $os = 'Unknown';

        // Detect OS
        if (str_contains($this->user_agent, 'Windows')) {
            $os = 'Windows';
        } elseif (str_contains($this->user_agent, 'Mac')) {
            $os = 'macOS';
        } elseif (str_contains($this->user_agent, 'Linux')) {
            $os = 'Linux';
        } elseif (str_contains($this->user_agent, 'Android')) {
            $os = 'Android';
        } elseif (str_contains($this->user_agent, 'iOS')) {
            $os = 'iOS';
        }

        // Detect Browser
        if (str_contains($this->user_agent, 'Chrome')) {
            $browser = 'Chrome';
        } elseif (str_contains($this->user_agent, 'Firefox')) {
            $browser = 'Firefox';
        } elseif (str_contains($this->user_agent, 'Safari')) {
            $browser = 'Safari';
        } elseif (str_contains($this->user_agent, 'Edge')) {
            $browser = 'Edge';
        }

        // Detect Device Type
        if (str_contains($this->user_agent, 'Mobile')) {
            $device = 'Mobile';
        } elseif (str_contains($this->user_agent, 'Tablet')) {
            $device = 'Tablet';
        } else {
            $device = 'Desktop';
        }

        return [
            'device' => $device,
            'browser' => $browser,
            'os' => $os,
        ];
    }

    public function getLocationAttribute(): string
    {
        $parts = array_filter([
            $this->location_city,
            $this->location_region,
            $this->location_country,
        ]);

        return empty($parts) ? 'Unknown' : implode(', ', $parts);
    }

    public function getStatusAttribute(): string
    {
        if (!$this->is_active) {
            return 'Ended';
        }

        $timeout = config('session.lifetime', 120); // minutes
        $isExpired = $this->last_activity->diffInMinutes(now()) > $timeout;

        return $isExpired ? 'Expired' : 'Active';
    }

    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'Active' => 'success',
            'Expired' => 'warning',
            'Ended' => 'gray',
            default => 'gray',
        };
    }

    public function isCurrentSession(): bool
    {
        return $this->session_id === session()->getId();
    }

    public function isSuspicious(): bool
    {
        // Check for suspicious patterns
        $user = $this->user;
        
        // Check if from unusual location
        $recentSessions = self::where('user_id', $user->id)
            ->where('login_at', '>=', Carbon::now()->subDays(30))
            ->where('id', '!=', $this->id)
            ->get();

        if ($recentSessions->isNotEmpty()) {
            $usualCountries = $recentSessions->pluck('location_country')->unique();
            
            if ($this->location_country && !$usualCountries->contains($this->location_country)) {
                return true;
            }
        }

        // Check for concurrent sessions from different IPs
        $concurrentSessions = self::where('user_id', $user->id)
            ->where('is_active', true)
            ->where('ip_address', '!=', $this->ip_address)
            ->where('last_activity', '>=', $this->last_activity->subMinutes(5))
            ->exists();

        return $concurrentSessions;
    }

    public function getDeviceIconAttribute(): string
    {
        $deviceInfo = $this->device_info;
        
        return match ($deviceInfo['device']) {
            'Mobile' => 'heroicon-o-device-phone-mobile',
            'Tablet' => 'heroicon-o-device-tablet',
            'Desktop' => 'heroicon-o-computer-desktop',
            default => 'heroicon-o-question-mark-circle',
        };
    }

    public function getBrowserIconAttribute(): string
    {
        $deviceInfo = $this->device_info;
        
        return match ($deviceInfo['browser']) {
            'Chrome' => 'heroicon-o-globe-alt',
            'Firefox' => 'heroicon-o-globe-alt',
            'Safari' => 'heroicon-o-globe-alt',
            'Edge' => 'heroicon-o-globe-alt',
            default => 'heroicon-o-globe-alt',
        };
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeExpired($query)
    {
        $timeout = config('session.lifetime', 120); // minutes
        $cutoff = Carbon::now()->subMinutes($timeout);
        
        return $query->where('is_active', true)
                    ->where('last_activity', '<', $cutoff);
    }

    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('login_at', '>=', Carbon::now()->subDays($days));
    }

    public function scopeForUser($query, User $user)
    {
        return $query->where('user_id', $user->id);
    }

    public function scopeSuspicious($query)
    {
        // This would need more complex logic in a real implementation
        return $query->where(function ($q) {
            // Sessions from unusual locations or concurrent sessions
            $q->whereNotNull('location_country')
              ->orWhere('is_active', true);
        });
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($session) {
            // Parse and store device information
            if ($session->user_agent) {
                $deviceInfo = app(\App\Services\SessionManagementService::class)
                    ->getDeviceInfo($session->user_agent);
                
                $session->device_type = $deviceInfo['device'];
                $session->browser = $deviceInfo['browser'];
                $session->operating_system = $deviceInfo['os'];
            }
        });
    }
}
