<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class UserSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'semester',
        'school_year_start',
        'theme_preference',
        'language_preference',
        'timezone_preference',
        'email_notifications',
        'push_notifications',
        'sms_notifications',
        'desktop_notifications',
        'notification_frequency',
        'privacy_profile_visible',
        'privacy_email_visible',
        'privacy_phone_visible',
        'privacy_show_online_status',
        'privacy_allow_direct_messages',
        'privacy_show_in_directory',
        'security_two_factor_enabled',
        'security_session_timeout',
        'security_login_alerts',
        'dashboard_layout',
        'dashboard_widgets',
        'table_preferences',
        'sidebar_collapsed',
        'items_per_page',
        'date_format',
        'time_format',
        'currency_format',
        'number_format',
        'custom_settings',
    ];

    protected $casts = [
        'user_id' => 'integer',
        'semester' => 'integer',
        'school_year_start' => 'integer',
        'email_notifications' => 'boolean',
        'push_notifications' => 'boolean',
        'sms_notifications' => 'boolean',
        'desktop_notifications' => 'boolean',
        'privacy_profile_visible' => 'boolean',
        'privacy_email_visible' => 'boolean',
        'privacy_phone_visible' => 'boolean',
        'privacy_show_online_status' => 'boolean',
        'privacy_allow_direct_messages' => 'boolean',
        'privacy_show_in_directory' => 'boolean',
        'security_two_factor_enabled' => 'boolean',
        'security_login_alerts' => 'boolean',
        'dashboard_widgets' => 'array',
        'table_preferences' => 'array',
        'sidebar_collapsed' => 'boolean',
        'items_per_page' => 'integer',
        'custom_settings' => 'array',
    ];

    /**
     * Get the user that owns the settings.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getDefaultSettings(): array
    {
        return [
            'theme_preference' => 'system',
            'language_preference' => 'en',
            'timezone_preference' => 'UTC',
            'email_notifications' => true,
            'push_notifications' => true,
            'sms_notifications' => false,
            'desktop_notifications' => true,
            'notification_frequency' => 'immediate',
            'privacy_profile_visible' => false,
            'privacy_email_visible' => false,
            'privacy_phone_visible' => false,
            'privacy_show_online_status' => true,
            'privacy_allow_direct_messages' => true,
            'privacy_show_in_directory' => true,
            'security_two_factor_enabled' => false,
            'security_session_timeout' => 120, // minutes
            'security_login_alerts' => true,
            'dashboard_layout' => 'default',
            'dashboard_widgets' => [],
            'table_preferences' => [],
            'sidebar_collapsed' => false,
            'items_per_page' => 25,
            'date_format' => 'Y-m-d',
            'time_format' => 'H:i:s',
            'currency_format' => 'USD',
            'number_format' => 'en_US',
            'custom_settings' => [],
        ];
    }

    public function getSetting(string $key, $default = null)
    {
        $defaults = $this->getDefaultSettings();
        return $this->getAttribute($key) ?? $defaults[$key] ?? $default;
    }

    public function setSetting(string $key, $value): void
    {
        $this->setAttribute($key, $value);
        $this->save();
    }

    public function resetToDefaults(): void
    {
        $defaults = $this->getDefaultSettings();
        $this->fill($defaults);
        $this->save();
    }

    public function getNotificationSettings(): array
    {
        return [
            'email_notifications' => $this->email_notifications,
            'push_notifications' => $this->push_notifications,
            'sms_notifications' => $this->sms_notifications,
            'desktop_notifications' => $this->desktop_notifications,
            'notification_frequency' => $this->notification_frequency,
        ];
    }

    public function getPrivacySettings(): array
    {
        return [
            'profile_visible' => $this->privacy_profile_visible,
            'email_visible' => $this->privacy_email_visible,
            'phone_visible' => $this->privacy_phone_visible,
            'show_online_status' => $this->privacy_show_online_status,
            'allow_direct_messages' => $this->privacy_allow_direct_messages,
            'show_in_directory' => $this->privacy_show_in_directory,
        ];
    }

    public function getSecuritySettings(): array
    {
        return [
            'two_factor_enabled' => $this->security_two_factor_enabled,
            'session_timeout' => $this->security_session_timeout,
            'login_alerts' => $this->security_login_alerts,
        ];
    }

    public function getDisplaySettings(): array
    {
        return [
            'theme_preference' => $this->theme_preference,
            'language_preference' => $this->language_preference,
            'timezone_preference' => $this->timezone_preference,
            'dashboard_layout' => $this->dashboard_layout,
            'sidebar_collapsed' => $this->sidebar_collapsed,
            'items_per_page' => $this->items_per_page,
            'date_format' => $this->date_format,
            'time_format' => $this->time_format,
        ];
    }

    public function updateNotificationSettings(array $settings): void
    {
        foreach ($settings as $key => $value) {
            if (in_array($key, ['email_notifications', 'push_notifications', 'sms_notifications', 'desktop_notifications', 'notification_frequency'])) {
                $this->setAttribute($key, $value);
            }
        }
        $this->save();
    }

    public function updatePrivacySettings(array $settings): void
    {
        foreach ($settings as $key => $value) {
            $privacyKey = 'privacy_' . $key;
            if (in_array($privacyKey, $this->fillable)) {
                $this->setAttribute($privacyKey, $value);
            }
        }
        $this->save();
    }

    public function updateSecuritySettings(array $settings): void
    {
        foreach ($settings as $key => $value) {
            $securityKey = 'security_' . $key;
            if (in_array($securityKey, $this->fillable)) {
                $this->setAttribute($securityKey, $value);
            }
        }
        $this->save();
    }

    public function updateDisplaySettings(array $settings): void
    {
        $allowedKeys = ['theme_preference', 'language_preference', 'timezone_preference', 'dashboard_layout', 'sidebar_collapsed', 'items_per_page', 'date_format', 'time_format'];
        foreach ($settings as $key => $value) {
            if (in_array($key, $allowedKeys)) {
                $this->setAttribute($key, $value);
            }
        }
        $this->save();
    }
}
