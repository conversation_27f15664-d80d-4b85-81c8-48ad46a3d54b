<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Carbon\Carbon;

final class UserActivityLog extends Model
{
    protected $fillable = [
        'user_id',
        'activity_type',
        'activity_description',
        'subject_type',
        'subject_id',
        'properties',
        'ip_address',
        'user_agent',
        'session_id',
        'url',
        'method',
        'status_code',
        'response_time',
        'created_at',
    ];

    protected $casts = [
        'user_id' => 'integer',
        'subject_id' => 'integer',
        'properties' => 'array',
        'status_code' => 'integer',
        'response_time' => 'integer',
        'created_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function subject(): MorphTo
    {
        return $this->morphTo();
    }

    // Activity Type Constants
    public const TYPE_LOGIN = 'login';
    public const TYPE_LOGOUT = 'logout';
    public const TYPE_LOGIN_FAILED = 'login_failed';
    public const TYPE_PASSWORD_CHANGED = 'password_changed';
    public const TYPE_PASSWORD_RESET = 'password_reset';
    public const TYPE_PROFILE_UPDATED = 'profile_updated';
    public const TYPE_SETTINGS_UPDATED = 'settings_updated';
    public const TYPE_ROLE_ASSIGNED = 'role_assigned';
    public const TYPE_ROLE_REMOVED = 'role_removed';
    public const TYPE_PERMISSION_GRANTED = 'permission_granted';
    public const TYPE_PERMISSION_REVOKED = 'permission_revoked';
    public const TYPE_ACCOUNT_ACTIVATED = 'account_activated';
    public const TYPE_ACCOUNT_DEACTIVATED = 'account_deactivated';
    public const TYPE_ACCOUNT_SUSPENDED = 'account_suspended';
    public const TYPE_ACCOUNT_LOCKED = 'account_locked';
    public const TYPE_ACCOUNT_UNLOCKED = 'account_unlocked';
    public const TYPE_EMAIL_VERIFIED = 'email_verified';
    public const TYPE_PHONE_VERIFIED = 'phone_verified';
    public const TYPE_TWO_FACTOR_ENABLED = 'two_factor_enabled';
    public const TYPE_TWO_FACTOR_DISABLED = 'two_factor_disabled';
    public const TYPE_DATA_EXPORTED = 'data_exported';
    public const TYPE_DATA_IMPORTED = 'data_imported';
    public const TYPE_FILE_UPLOADED = 'file_uploaded';
    public const TYPE_FILE_DOWNLOADED = 'file_downloaded';
    public const TYPE_RECORD_CREATED = 'record_created';
    public const TYPE_RECORD_UPDATED = 'record_updated';
    public const TYPE_RECORD_DELETED = 'record_deleted';
    public const TYPE_RECORD_VIEWED = 'record_viewed';
    public const TYPE_BULK_ACTION = 'bulk_action';
    public const TYPE_SYSTEM_ACCESS = 'system_access';
    public const TYPE_API_ACCESS = 'api_access';

    public static function getActivityTypes(): array
    {
        return [
            self::TYPE_LOGIN => 'User Login',
            self::TYPE_LOGOUT => 'User Logout',
            self::TYPE_LOGIN_FAILED => 'Failed Login Attempt',
            self::TYPE_PASSWORD_CHANGED => 'Password Changed',
            self::TYPE_PASSWORD_RESET => 'Password Reset',
            self::TYPE_PROFILE_UPDATED => 'Profile Updated',
            self::TYPE_SETTINGS_UPDATED => 'Settings Updated',
            self::TYPE_ROLE_ASSIGNED => 'Role Assigned',
            self::TYPE_ROLE_REMOVED => 'Role Removed',
            self::TYPE_PERMISSION_GRANTED => 'Permission Granted',
            self::TYPE_PERMISSION_REVOKED => 'Permission Revoked',
            self::TYPE_ACCOUNT_ACTIVATED => 'Account Activated',
            self::TYPE_ACCOUNT_DEACTIVATED => 'Account Deactivated',
            self::TYPE_ACCOUNT_SUSPENDED => 'Account Suspended',
            self::TYPE_ACCOUNT_LOCKED => 'Account Locked',
            self::TYPE_ACCOUNT_UNLOCKED => 'Account Unlocked',
            self::TYPE_EMAIL_VERIFIED => 'Email Verified',
            self::TYPE_PHONE_VERIFIED => 'Phone Verified',
            self::TYPE_TWO_FACTOR_ENABLED => '2FA Enabled',
            self::TYPE_TWO_FACTOR_DISABLED => '2FA Disabled',
            self::TYPE_DATA_EXPORTED => 'Data Exported',
            self::TYPE_DATA_IMPORTED => 'Data Imported',
            self::TYPE_FILE_UPLOADED => 'File Uploaded',
            self::TYPE_FILE_DOWNLOADED => 'File Downloaded',
            self::TYPE_RECORD_CREATED => 'Record Created',
            self::TYPE_RECORD_UPDATED => 'Record Updated',
            self::TYPE_RECORD_DELETED => 'Record Deleted',
            self::TYPE_RECORD_VIEWED => 'Record Viewed',
            self::TYPE_BULK_ACTION => 'Bulk Action',
            self::TYPE_SYSTEM_ACCESS => 'System Access',
            self::TYPE_API_ACCESS => 'API Access',
        ];
    }

    public static function log(
        int $userId,
        string $activityType,
        string $description,
        ?string $subjectType = null,
        ?int $subjectId = null,
        array $properties = [],
        ?string $ipAddress = null,
        ?string $userAgent = null,
        ?string $sessionId = null,
        ?string $url = null,
        ?string $method = null,
        ?int $statusCode = null,
        ?int $responseTime = null
    ): self {
        return self::create([
            'user_id' => $userId,
            'activity_type' => $activityType,
            'activity_description' => $description,
            'subject_type' => $subjectType,
            'subject_id' => $subjectId,
            'properties' => $properties,
            'ip_address' => $ipAddress ?? request()->ip(),
            'user_agent' => $userAgent ?? request()->userAgent(),
            'session_id' => $sessionId ?? session()->getId(),
            'url' => $url ?? request()->fullUrl(),
            'method' => $method ?? request()->method(),
            'status_code' => $statusCode,
            'response_time' => $responseTime,
        ]);
    }

    public static function logLogin(User $user, bool $successful = true, ?string $reason = null): self
    {
        $type = $successful ? self::TYPE_LOGIN : self::TYPE_LOGIN_FAILED;
        $description = $successful 
            ? "User {$user->name} logged in successfully"
            : "Failed login attempt for {$user->email}" . ($reason ? ": {$reason}" : '');

        return self::log(
            $user->id,
            $type,
            $description,
            User::class,
            $user->id,
            $successful ? [] : ['reason' => $reason]
        );
    }

    public static function logLogout(User $user): self
    {
        return self::log(
            $user->id,
            self::TYPE_LOGOUT,
            "User {$user->name} logged out",
            User::class,
            $user->id
        );
    }

    public static function logPasswordChange(User $user): self
    {
        return self::log(
            $user->id,
            self::TYPE_PASSWORD_CHANGED,
            "User {$user->name} changed their password",
            User::class,
            $user->id
        );
    }

    public static function logProfileUpdate(User $user, array $changes = []): self
    {
        return self::log(
            $user->id,
            self::TYPE_PROFILE_UPDATED,
            "User {$user->name} updated their profile",
            User::class,
            $user->id,
            ['changes' => $changes]
        );
    }

    public static function logAccountStatusChange(User $user, string $oldStatus, string $newStatus, ?User $changedBy = null): self
    {
        $changedByText = $changedBy ? " by {$changedBy->name}" : '';
        return self::log(
            $changedBy ? $changedBy->id : $user->id,
            match($newStatus) {
                'active' => self::TYPE_ACCOUNT_ACTIVATED,
                'inactive' => self::TYPE_ACCOUNT_DEACTIVATED,
                'suspended' => self::TYPE_ACCOUNT_SUSPENDED,
                'locked' => self::TYPE_ACCOUNT_LOCKED,
                default => self::TYPE_PROFILE_UPDATED,
            },
            "User {$user->name} account status changed from {$oldStatus} to {$newStatus}{$changedByText}",
            User::class,
            $user->id,
            ['old_status' => $oldStatus, 'new_status' => $newStatus, 'changed_by' => $changedBy?->id]
        );
    }

    public static function logRoleChange(User $user, string $action, string $roleName, ?User $changedBy = null): self
    {
        $type = $action === 'assigned' ? self::TYPE_ROLE_ASSIGNED : self::TYPE_ROLE_REMOVED;
        $changedByText = $changedBy ? " by {$changedBy->name}" : '';
        
        return self::log(
            $changedBy ? $changedBy->id : $user->id,
            $type,
            "Role '{$roleName}' {$action} to user {$user->name}{$changedByText}",
            User::class,
            $user->id,
            ['role' => $roleName, 'action' => $action, 'changed_by' => $changedBy?->id]
        );
    }

    public static function logRecordAction(User $user, string $action, Model $record): self
    {
        $type = match($action) {
            'created' => self::TYPE_RECORD_CREATED,
            'updated' => self::TYPE_RECORD_UPDATED,
            'deleted' => self::TYPE_RECORD_DELETED,
            'viewed' => self::TYPE_RECORD_VIEWED,
            default => self::TYPE_SYSTEM_ACCESS,
        };

        $modelName = class_basename($record);
        $recordId = $record->getKey();

        return self::log(
            $user->id,
            $type,
            "User {$user->name} {$action} {$modelName} (ID: {$recordId})",
            get_class($record),
            $recordId,
            ['action' => $action, 'model' => $modelName]
        );
    }

    public function getActivityTypeLabel(): string
    {
        return self::getActivityTypes()[$this->activity_type] ?? $this->activity_type;
    }

    public function getActivityIcon(): string
    {
        return match($this->activity_type) {
            self::TYPE_LOGIN => 'heroicon-o-arrow-right-on-rectangle',
            self::TYPE_LOGOUT => 'heroicon-o-arrow-left-on-rectangle',
            self::TYPE_LOGIN_FAILED => 'heroicon-o-exclamation-triangle',
            self::TYPE_PASSWORD_CHANGED, self::TYPE_PASSWORD_RESET => 'heroicon-o-key',
            self::TYPE_PROFILE_UPDATED, self::TYPE_SETTINGS_UPDATED => 'heroicon-o-user-circle',
            self::TYPE_ROLE_ASSIGNED, self::TYPE_ROLE_REMOVED => 'heroicon-o-shield-check',
            self::TYPE_ACCOUNT_ACTIVATED => 'heroicon-o-check-circle',
            self::TYPE_ACCOUNT_DEACTIVATED, self::TYPE_ACCOUNT_SUSPENDED => 'heroicon-o-x-circle',
            self::TYPE_ACCOUNT_LOCKED => 'heroicon-o-lock-closed',
            self::TYPE_ACCOUNT_UNLOCKED => 'heroicon-o-lock-open',
            self::TYPE_EMAIL_VERIFIED, self::TYPE_PHONE_VERIFIED => 'heroicon-o-check-badge',
            self::TYPE_TWO_FACTOR_ENABLED, self::TYPE_TWO_FACTOR_DISABLED => 'heroicon-o-device-phone-mobile',
            self::TYPE_DATA_EXPORTED, self::TYPE_DATA_IMPORTED => 'heroicon-o-arrow-up-tray',
            self::TYPE_FILE_UPLOADED => 'heroicon-o-cloud-arrow-up',
            self::TYPE_FILE_DOWNLOADED => 'heroicon-o-cloud-arrow-down',
            self::TYPE_RECORD_CREATED => 'heroicon-o-plus-circle',
            self::TYPE_RECORD_UPDATED => 'heroicon-o-pencil-square',
            self::TYPE_RECORD_DELETED => 'heroicon-o-trash',
            self::TYPE_RECORD_VIEWED => 'heroicon-o-eye',
            self::TYPE_BULK_ACTION => 'heroicon-o-squares-plus',
            self::TYPE_SYSTEM_ACCESS, self::TYPE_API_ACCESS => 'heroicon-o-computer-desktop',
            default => 'heroicon-o-information-circle',
        };
    }

    public function getActivityColor(): string
    {
        return match($this->activity_type) {
            self::TYPE_LOGIN, self::TYPE_ACCOUNT_ACTIVATED, self::TYPE_ACCOUNT_UNLOCKED => 'success',
            self::TYPE_LOGIN_FAILED, self::TYPE_ACCOUNT_SUSPENDED, self::TYPE_ACCOUNT_LOCKED => 'danger',
            self::TYPE_LOGOUT, self::TYPE_ACCOUNT_DEACTIVATED => 'warning',
            self::TYPE_PASSWORD_CHANGED, self::TYPE_PASSWORD_RESET => 'info',
            self::TYPE_RECORD_DELETED => 'danger',
            self::TYPE_RECORD_CREATED => 'success',
            default => 'gray',
        };
    }

    public function scopeForUser($query, User $user)
    {
        return $query->where('user_id', $user->id);
    }

    public function scopeOfType($query, string $type)
    {
        return $query->where('activity_type', $type);
    }

    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', Carbon::now()->subDays($days));
    }

    public function scopeSecurityRelated($query)
    {
        return $query->whereIn('activity_type', [
            self::TYPE_LOGIN,
            self::TYPE_LOGOUT,
            self::TYPE_LOGIN_FAILED,
            self::TYPE_PASSWORD_CHANGED,
            self::TYPE_PASSWORD_RESET,
            self::TYPE_TWO_FACTOR_ENABLED,
            self::TYPE_TWO_FACTOR_DISABLED,
            self::TYPE_ACCOUNT_LOCKED,
            self::TYPE_ACCOUNT_UNLOCKED,
        ]);
    }
}
