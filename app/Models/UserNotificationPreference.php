<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class UserNotificationPreference extends Model
{
    protected $fillable = [
        'user_id',
        'notification_type',
        'channel',
        'enabled',
        'frequency',
        'quiet_hours_start',
        'quiet_hours_end',
        'timezone',
        'settings',
    ];

    protected $casts = [
        'user_id' => 'integer',
        'enabled' => 'boolean',
        'quiet_hours_start' => 'datetime:H:i',
        'quiet_hours_end' => 'datetime:H:i',
        'settings' => 'array',
    ];

    // Notification Types
    public const TYPE_SECURITY_ALERTS = 'security_alerts';
    public const TYPE_LOGIN_NOTIFICATIONS = 'login_notifications';
    public const TYPE_PASSWORD_CHANGES = 'password_changes';
    public const TYPE_PROFILE_UPDATES = 'profile_updates';
    public const TYPE_SYSTEM_ANNOUNCEMENTS = 'system_announcements';
    public const TYPE_ACCOUNT_CHANGES = 'account_changes';
    public const TYPE_ROLE_CHANGES = 'role_changes';
    public const TYPE_DATA_EXPORTS = 'data_exports';
    public const TYPE_BULK_OPERATIONS = 'bulk_operations';
    public const TYPE_MAINTENANCE_NOTICES = 'maintenance_notices';
    public const TYPE_FEATURE_UPDATES = 'feature_updates';
    public const TYPE_MARKETING = 'marketing';
    public const TYPE_NEWSLETTERS = 'newsletters';
    public const TYPE_REMINDERS = 'reminders';
    public const TYPE_DEADLINES = 'deadlines';

    // Channels
    public const CHANNEL_EMAIL = 'email';
    public const CHANNEL_SMS = 'sms';
    public const CHANNEL_PUSH = 'push';
    public const CHANNEL_IN_APP = 'in_app';
    public const CHANNEL_DESKTOP = 'desktop';
    public const CHANNEL_SLACK = 'slack';
    public const CHANNEL_TEAMS = 'teams';

    // Frequencies
    public const FREQUENCY_IMMEDIATE = 'immediate';
    public const FREQUENCY_HOURLY = 'hourly';
    public const FREQUENCY_DAILY = 'daily';
    public const FREQUENCY_WEEKLY = 'weekly';
    public const FREQUENCY_MONTHLY = 'monthly';

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public static function getNotificationTypes(): array
    {
        return [
            self::TYPE_SECURITY_ALERTS => 'Security Alerts',
            self::TYPE_LOGIN_NOTIFICATIONS => 'Login Notifications',
            self::TYPE_PASSWORD_CHANGES => 'Password Changes',
            self::TYPE_PROFILE_UPDATES => 'Profile Updates',
            self::TYPE_SYSTEM_ANNOUNCEMENTS => 'System Announcements',
            self::TYPE_ACCOUNT_CHANGES => 'Account Changes',
            self::TYPE_ROLE_CHANGES => 'Role Changes',
            self::TYPE_DATA_EXPORTS => 'Data Exports',
            self::TYPE_BULK_OPERATIONS => 'Bulk Operations',
            self::TYPE_MAINTENANCE_NOTICES => 'Maintenance Notices',
            self::TYPE_FEATURE_UPDATES => 'Feature Updates',
            self::TYPE_MARKETING => 'Marketing',
            self::TYPE_NEWSLETTERS => 'Newsletters',
            self::TYPE_REMINDERS => 'Reminders',
            self::TYPE_DEADLINES => 'Deadlines',
        ];
    }

    public static function getChannels(): array
    {
        return [
            self::CHANNEL_EMAIL => 'Email',
            self::CHANNEL_SMS => 'SMS',
            self::CHANNEL_PUSH => 'Push Notifications',
            self::CHANNEL_IN_APP => 'In-App Notifications',
            self::CHANNEL_DESKTOP => 'Desktop Notifications',
            self::CHANNEL_SLACK => 'Slack',
            self::CHANNEL_TEAMS => 'Microsoft Teams',
        ];
    }

    public static function getFrequencies(): array
    {
        return [
            self::FREQUENCY_IMMEDIATE => 'Immediate',
            self::FREQUENCY_HOURLY => 'Hourly Digest',
            self::FREQUENCY_DAILY => 'Daily Digest',
            self::FREQUENCY_WEEKLY => 'Weekly Digest',
            self::FREQUENCY_MONTHLY => 'Monthly Digest',
        ];
    }

    public static function getDefaultPreferences(): array
    {
        return [
            // Security notifications - always enabled for important channels
            [
                'notification_type' => self::TYPE_SECURITY_ALERTS,
                'channel' => self::CHANNEL_EMAIL,
                'enabled' => true,
                'frequency' => self::FREQUENCY_IMMEDIATE,
            ],
            [
                'notification_type' => self::TYPE_SECURITY_ALERTS,
                'channel' => self::CHANNEL_IN_APP,
                'enabled' => true,
                'frequency' => self::FREQUENCY_IMMEDIATE,
            ],
            [
                'notification_type' => self::TYPE_LOGIN_NOTIFICATIONS,
                'channel' => self::CHANNEL_EMAIL,
                'enabled' => true,
                'frequency' => self::FREQUENCY_IMMEDIATE,
            ],
            [
                'notification_type' => self::TYPE_PASSWORD_CHANGES,
                'channel' => self::CHANNEL_EMAIL,
                'enabled' => true,
                'frequency' => self::FREQUENCY_IMMEDIATE,
            ],
            
            // System notifications
            [
                'notification_type' => self::TYPE_SYSTEM_ANNOUNCEMENTS,
                'channel' => self::CHANNEL_EMAIL,
                'enabled' => true,
                'frequency' => self::FREQUENCY_IMMEDIATE,
            ],
            [
                'notification_type' => self::TYPE_SYSTEM_ANNOUNCEMENTS,
                'channel' => self::CHANNEL_IN_APP,
                'enabled' => true,
                'frequency' => self::FREQUENCY_IMMEDIATE,
            ],
            
            // Account changes
            [
                'notification_type' => self::TYPE_ACCOUNT_CHANGES,
                'channel' => self::CHANNEL_EMAIL,
                'enabled' => true,
                'frequency' => self::FREQUENCY_IMMEDIATE,
            ],
            [
                'notification_type' => self::TYPE_ROLE_CHANGES,
                'channel' => self::CHANNEL_EMAIL,
                'enabled' => true,
                'frequency' => self::FREQUENCY_IMMEDIATE,
            ],
            
            // Optional notifications - disabled by default
            [
                'notification_type' => self::TYPE_MARKETING,
                'channel' => self::CHANNEL_EMAIL,
                'enabled' => false,
                'frequency' => self::FREQUENCY_WEEKLY,
            ],
            [
                'notification_type' => self::TYPE_NEWSLETTERS,
                'channel' => self::CHANNEL_EMAIL,
                'enabled' => false,
                'frequency' => self::FREQUENCY_MONTHLY,
            ],
        ];
    }

    public static function createDefaultPreferences(User $user): void
    {
        $defaults = self::getDefaultPreferences();
        
        foreach ($defaults as $preference) {
            self::updateOrCreate([
                'user_id' => $user->id,
                'notification_type' => $preference['notification_type'],
                'channel' => $preference['channel'],
            ], $preference);
        }
    }

    public function isInQuietHours(): bool
    {
        if (!$this->quiet_hours_start || !$this->quiet_hours_end) {
            return false;
        }

        $now = now($this->timezone ?? 'UTC');
        $start = $this->quiet_hours_start->setDate($now->year, $now->month, $now->day);
        $end = $this->quiet_hours_end->setDate($now->year, $now->month, $now->day);

        // Handle overnight quiet hours (e.g., 22:00 to 06:00)
        if ($start->greaterThan($end)) {
            return $now->greaterThanOrEqualTo($start) || $now->lessThanOrEqualTo($end);
        }

        return $now->between($start, $end);
    }

    public function shouldSendNotification(): bool
    {
        if (!$this->enabled) {
            return false;
        }

        // Always send immediate security notifications
        if ($this->notification_type === self::TYPE_SECURITY_ALERTS && 
            $this->frequency === self::FREQUENCY_IMMEDIATE) {
            return true;
        }

        // Check quiet hours for non-critical notifications
        if ($this->isInQuietHours()) {
            return false;
        }

        return true;
    }

    public function getNextDigestTime(): ?\Carbon\Carbon
    {
        if ($this->frequency === self::FREQUENCY_IMMEDIATE) {
            return null;
        }

        $now = now($this->timezone ?? 'UTC');

        return match ($this->frequency) {
            self::FREQUENCY_HOURLY => $now->addHour()->startOfHour(),
            self::FREQUENCY_DAILY => $now->addDay()->startOfDay()->addHours(9), // 9 AM
            self::FREQUENCY_WEEKLY => $now->addWeek()->startOfWeek()->addHours(9),
            self::FREQUENCY_MONTHLY => $now->addMonth()->startOfMonth()->addHours(9),
            default => null,
        };
    }

    public function scopeForUser($query, User $user)
    {
        return $query->where('user_id', $user->id);
    }

    public function scopeEnabled($query)
    {
        return $query->where('enabled', true);
    }

    public function scopeForType($query, string $type)
    {
        return $query->where('notification_type', $type);
    }

    public function scopeForChannel($query, string $channel)
    {
        return $query->where('channel', $channel);
    }

    public function scopeImmediate($query)
    {
        return $query->where('frequency', self::FREQUENCY_IMMEDIATE);
    }

    public function scopeDigest($query)
    {
        return $query->whereIn('frequency', [
            self::FREQUENCY_HOURLY,
            self::FREQUENCY_DAILY,
            self::FREQUENCY_WEEKLY,
            self::FREQUENCY_MONTHLY,
        ]);
    }

    public function getChannelLabel(): string
    {
        return self::getChannels()[$this->channel] ?? $this->channel;
    }

    public function getTypeLabel(): string
    {
        return self::getNotificationTypes()[$this->notification_type] ?? $this->notification_type;
    }

    public function getFrequencyLabel(): string
    {
        return self::getFrequencies()[$this->frequency] ?? $this->frequency;
    }
}
