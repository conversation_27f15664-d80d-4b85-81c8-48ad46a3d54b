<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Schmeits\FilamentUmami\Concerns\HasFilter;

class WebsiteAnalytics extends Page
{
    use HasFilter;
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.pages.website-analytics';

    // header widgets
    public function getHeaderWidgets(): array
    {
        return [
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetStatsGrouped::class,
      
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetStatsVisits::class,
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetStatsBounces::class,
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetStatsTotalTime::class,
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetTableUrls::class,
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetTableTitle::class,
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetTableReferrers::class,
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetTableCountry::class,
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetTableRegion::class,
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetTableCity::class,
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetTableDevice::class,
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetTableOs::class,
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetTableBrowser::class,
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetTableLanguage::class,
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetTableScreen::class,
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetTableEvents::class,
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetTableQuery::class,
         
            // grouped table widgets
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetTableGroupedPages::class,
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetTableGroupedGeo::class,
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetTableGroupedClientInfo::class,
         
            // chart widgets
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetGraphPageViews::class,
            \Schmeits\FilamentUmami\Widgets\UmamiWidgetGraphSessions::class,
            // \Schmeits\FilamentUmami\Widgets\UmamiWidgetGraphEvents::class,
        ];
    }
}
