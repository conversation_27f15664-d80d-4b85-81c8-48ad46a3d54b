<?php

namespace App\Filament\Resources\StudentEnrollmentResource\Pages;

use App\Filament\Resources\StudentEnrollmentResource;
use App\Models\Resource;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Concerns\InteractsWithRecord;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ViewResource extends Page
{
    use InteractsWithRecord;

    protected static string $resource = StudentEnrollmentResource::class;

    protected static string $view = 'filament.resources.student-enrollment-resource.pages.view-resource';

    public Resource $resourceData;

    public function mount(int|string $record, $resourceId): void
    {
        try {
            $this->record = $this->resolveRecord($record);

            Log::info('Mounting ViewResource', [
                'record_id' => $record,
                'resource_id' => $resourceId,
            ]);

            $this->resourceData = Resource::findOrFail($resourceId);

            Log::info('Resource found', [
                'resource_type' => $this->resourceData->type,
                'file_path' => $this->resourceData->file_path,
                'disk' => $this->resourceData->disk,
            ]);

            if ($this->resourceData->resourceable_type !== 'App\\Models\\StudentEnrollment' || $this->resourceData->resourceable_id != $record) {
                Log::error('Resource mismatch', [
                    'resource_id' => $resourceId,
                    'resourceable_id' => $this->resourceData->resourceable_id,
                    'resourceable_type' => $this->resourceData->resourceable_type,
                    'record_id' => $record,
                ]);

                Notification::make()
                    ->title('Error')
                    ->body('Resource not found or does not belong to this record.')
                    ->danger()
                    ->send();

                $this->redirect(StudentEnrollmentResource::getUrl());

                return;
            }

        } catch (\Exception $e) {
            Log::error('Error in mount', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            Notification::make()
                ->title('Error')
                ->body('An error occurred while loading the resource: '.$e->getMessage())
                ->danger()
                ->send();

            $this->redirect(StudentEnrollmentResource::getUrl());

            return;
        }
    }

    public function getPdfUrl(): ?string
    {
        try {
            if (! $this->resourceData) {
                return null;
            }

            Log::info('Getting PDF URL', [
                'resource_id' => $this->resourceData->id,
                'file_path' => $this->resourceData->file_path,
                'disk' => $this->resourceData->disk,
                'file_name' => $this->resourceData->file_name,
            ]);

            // Always use the view-resource route which handles all file serving logic
            return route('view-resource', ['resource' => $this->resourceData->id]);

        } catch (\Exception $e) {
            Log::error('Error getting PDF URL', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }
}
