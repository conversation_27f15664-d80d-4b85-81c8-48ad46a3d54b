<?php

declare(strict_types=1);

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Tabs;
use Filament\Support\Enums\FontWeight;

final class ViewUser extends ViewRecord
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\Action::make('activate')
                ->label('Activate')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->requiresConfirmation()
                ->visible(fn () => !$this->record->isActive())
                ->action(fn () => $this->record->activate()),
            Actions\Action::make('deactivate')
                ->label('Deactivate')
                ->icon('heroicon-o-x-circle')
                ->color('warning')
                ->requiresConfirmation()
                ->visible(fn () => $this->record->isActive())
                ->action(fn () => $this->record->deactivate()),
            Actions\Action::make('suspend')
                ->label('Suspend')
                ->icon('heroicon-o-no-symbol')
                ->color('danger')
                ->requiresConfirmation()
                ->visible(fn () => !$this->record->isSuspended())
                ->action(fn () => $this->record->suspend()),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Tabs::make('User Details')
                    ->tabs([
                        Tabs\Tab::make('Overview')
                            ->schema([
                                Section::make('Basic Information')
                                    ->schema([
                                        Grid::make(3)
                                            ->schema([
                                                ImageEntry::make('avatar_url')
                                                    ->label('Avatar')
                                                    ->circular()
                                                    ->defaultImageUrl(fn ($record) => 'https://ui-avatars.com/api/?name=' . urlencode($record->name) . '&color=7F9CF5&background=EBF4FF'),
                                                TextEntry::make('name')
                                                    ->weight(FontWeight::Bold)
                                                    ->size('lg'),
                                                TextEntry::make('account_status')
                                                    ->badge()
                                                    ->color(fn (string $state): string => match ($state) {
                                                        'active' => 'success',
                                                        'inactive' => 'gray',
                                                        'suspended', 'locked' => 'danger',
                                                        'pending' => 'warning',
                                                        default => 'gray',
                                                    }),
                                            ]),
                                        Grid::make(2)
                                            ->schema([
                                                TextEntry::make('email')
                                                    ->icon('heroicon-m-envelope')
                                                    ->copyable(),
                                                TextEntry::make('phone')
                                                    ->icon('heroicon-m-phone')
                                                    ->placeholder('Not provided'),
                                            ]),
                                        Grid::make(3)
                                            ->schema([
                                                TextEntry::make('employee_id')
                                                    ->placeholder('Not assigned'),
                                                TextEntry::make('language')
                                                    ->placeholder('Not set'),
                                                TextEntry::make('timezone')
                                                    ->placeholder('UTC'),
                                            ]),
                                    ]),
                                Section::make('Personal Information')
                                    ->schema([
                                        Grid::make(3)
                                            ->schema([
                                                TextEntry::make('date_of_birth')
                                                    ->date()
                                                    ->placeholder('Not provided'),
                                                TextEntry::make('gender')
                                                    ->placeholder('Not specified'),
                                                TextEntry::make('age')
                                                    ->suffix(' years old')
                                                    ->placeholder('Unknown'),
                                            ]),
                                        TextEntry::make('bio')
                                            ->placeholder('No bio provided')
                                            ->columnSpanFull(),
                                    ]),
                            ]),

                        Tabs\Tab::make('Professional')
                            ->schema([
                                Section::make('Work Information')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                TextEntry::make('department')
                                                    ->icon('heroicon-m-building-office')
                                                    ->placeholder('Not assigned'),
                                                TextEntry::make('position')
                                                    ->placeholder('Not specified'),
                                            ]),
                                        Grid::make(2)
                                            ->schema([
                                                TextEntry::make('hire_date')
                                                    ->date()
                                                    ->placeholder('Not recorded'),
                                                TextEntry::make('time_in_company')
                                                    ->placeholder('Unknown'),
                                            ]),
                                        TextEntry::make('manager.name')
                                            ->label('Manager')
                                            ->placeholder('No manager assigned'),
                                    ]),
                                Section::make('Roles & Permissions')
                                    ->schema([
                                        TextEntry::make('roles.name')
                                            ->label('Assigned Roles')
                                            ->badge()
                                            ->separator(',')
                                            ->placeholder('No roles assigned'),
                                    ]),
                            ]),

                        Tabs\Tab::make('Security')
                            ->schema([
                                Section::make('Authentication')
                                    ->schema([
                                        Grid::make(3)
                                            ->schema([
                                                IconEntry::make('two_factor_enabled')
                                                    ->label('2FA Enabled')
                                                    ->boolean(),
                                                IconEntry::make('must_change_password')
                                                    ->label('Must Change Password')
                                                    ->boolean(),
                                                IconEntry::make('active_status')
                                                    ->label('Active Status')
                                                    ->boolean(),
                                            ]),
                                        Grid::make(2)
                                            ->schema([
                                                TextEntry::make('last_login_at')
                                                    ->label('Last Login')
                                                    ->dateTime()
                                                    ->since()
                                                    ->placeholder('Never logged in'),
                                                TextEntry::make('last_login_ip')
                                                    ->label('Last Login IP')
                                                    ->placeholder('Unknown'),
                                            ]),
                                        Grid::make(2)
                                            ->schema([
                                                TextEntry::make('login_attempts')
                                                    ->label('Failed Login Attempts'),
                                                TextEntry::make('locked_until')
                                                    ->label('Locked Until')
                                                    ->dateTime()
                                                    ->placeholder('Not locked'),
                                            ]),
                                    ]),
                            ]),

                        Tabs\Tab::make('Contact & Address')
                            ->schema([
                                Section::make('Address Information')
                                    ->schema([
                                        TextEntry::make('address')
                                            ->placeholder('No address provided')
                                            ->columnSpanFull(),
                                        Grid::make(3)
                                            ->schema([
                                                TextEntry::make('city')
                                                    ->placeholder('Not specified'),
                                                TextEntry::make('state')
                                                    ->placeholder('Not specified'),
                                                TextEntry::make('postal_code')
                                                    ->placeholder('Not specified'),
                                            ]),
                                        TextEntry::make('country')
                                            ->placeholder('Not specified'),
                                    ]),
                                Section::make('Online Presence')
                                    ->schema([
                                        TextEntry::make('website')
                                            ->url()
                                            ->openUrlInNewTab()
                                            ->placeholder('No website'),
                                        TextEntry::make('social_links')
                                            ->label('Social Links')
                                            ->placeholder('No social links')
                                            ->formatStateUsing(function ($state) {
                                                if (!$state || !is_array($state)) {
                                                    return 'No social links';
                                                }
                                                return collect($state)->map(fn ($url, $platform) => "{$platform}: {$url}")->join(', ');
                                            }),
                                    ]),
                            ]),

                        Tabs\Tab::make('System Information')
                            ->schema([
                                Section::make('Account Details')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                TextEntry::make('created_at')
                                                    ->label('Account Created')
                                                    ->dateTime(),
                                                TextEntry::make('updated_at')
                                                    ->label('Last Updated')
                                                    ->dateTime(),
                                            ]),
                                        Grid::make(2)
                                            ->schema([
                                                TextEntry::make('email_verified_at')
                                                    ->label('Email Verified')
                                                    ->dateTime()
                                                    ->placeholder('Not verified'),
                                                TextEntry::make('phone_verified_at')
                                                    ->label('Phone Verified')
                                                    ->dateTime()
                                                    ->placeholder('Not verified'),
                                            ]),
                                        TextEntry::make('profile_completed_at')
                                            ->label('Profile Completed')
                                            ->dateTime()
                                            ->placeholder('Incomplete'),
                                    ]),
                            ]),
                    ])
                    ->columnSpanFull(),
            ]);
    }
}
