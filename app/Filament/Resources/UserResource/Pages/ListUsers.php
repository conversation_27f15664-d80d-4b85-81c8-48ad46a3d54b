<?php

declare(strict_types=1);

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Forms\Components\FileUpload;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;

final class ListUsers extends ListRecords
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Actions\Action::make('import')
                ->label('Import Users')
                ->icon('heroicon-o-arrow-up-tray')
                ->color('info')
                ->form([
                    FileUpload::make('file')
                        ->label('CSV File')
                        ->acceptedFileTypes(['text/csv', 'application/csv'])
                        ->required()
                        ->helperText('Upload a CSV file with user data. Download the template below for the correct format.'),
                ])
                ->action(function (array $data): void {
                    $file = $data['file'];
                    $importService = app(\App\Services\UserImportExportService::class);

                    try {
                        $results = $importService->importFromCsv($file, Auth::user());

                        $message = "Import completed: {$results['imported']} created, {$results['updated']} updated";

                        if (!empty($results['errors'])) {
                            $message .= ". " . count($results['errors']) . " errors occurred.";
                        }

                        Notification::make()
                            ->title('Import Complete')
                            ->body($message)
                            ->success()
                            ->send();

                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Import Failed')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),
            Actions\Action::make('downloadTemplate')
                ->label('Download Template')
                ->icon('heroicon-o-document-arrow-down')
                ->color('gray')
                ->action(function (): void {
                    $importService = app(\App\Services\UserImportExportService::class);
                    $csv = $importService->exportUserTemplate();

                    $filename = 'user_import_template.csv';
                    Storage::disk('local')->put('templates/' . $filename, $csv);

                    Notification::make()
                        ->title('Template Ready')
                        ->body('User import template has been generated')
                        ->success()
                        ->send();
                }),
            Actions\Action::make('exportAll')
                ->label('Export All Users')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                ->requiresConfirmation()
                ->action(function (): void {
                    $importService = app(\App\Services\UserImportExportService::class);
                    $csv = $importService->exportToCsv();

                    $filename = 'all_users_export_' . now()->format('Y-m-d_H-i-s') . '.csv';
                    Storage::disk('local')->put('exports/' . $filename, $csv);

                    Notification::make()
                        ->title('Export Complete')
                        ->body("All users exported to {$filename}")
                        ->success()
                        ->send();
                }),
        ];
    }
}
