<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\UserProfileResource\Pages;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Notifications\Notification;

final class UserProfileResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-circle';

    protected static ?string $navigationGroup = 'User Management';

    protected static ?string $navigationLabel = 'User Profiles';

    protected static ?string $modelLabel = 'User Profile';

    protected static ?string $pluralModelLabel = 'User Profiles';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('Profile Management')
                    ->tabs([
                        Tabs\Tab::make('Profile Completion')
                            ->schema([
                                Section::make('Profile Status')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                TextInput::make('profile_completion_percentage')
                                                    ->label('Profile Completion')
                                                    ->disabled()
                                                    ->suffix('%')
                                                    ->default(fn ($record) => $record ? $record->getProfileCompletionPercentage() : 0),
                                                Toggle::make('profile_completed')
                                                    ->label('Mark as Complete')
                                                    ->disabled()
                                                    ->default(fn ($record) => $record ? $record->isProfileComplete() : false),
                                            ]),
                                    ]),
                                Section::make('Required Information')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                TextInput::make('name')
                                                    ->required()
                                                    ->maxLength(255),
                                                TextInput::make('email')
                                                    ->email()
                                                    ->required()
                                                    ->unique(ignoreRecord: true)
                                                    ->maxLength(255),
                                            ]),
                                        Grid::make(2)
                                            ->schema([
                                                TextInput::make('phone')
                                                    ->tel()
                                                    ->maxLength(20),
                                                DatePicker::make('date_of_birth')
                                                    ->maxDate(now()->subYears(16)),
                                            ]),
                                    ]),
                            ]),

                        Tabs\Tab::make('Personal Information')
                            ->schema([
                                Section::make('Basic Details')
                                    ->schema([
                                        Grid::make(3)
                                            ->schema([
                                                Select::make('gender')
                                                    ->options([
                                                        'male' => 'Male',
                                                        'female' => 'Female',
                                                        'other' => 'Other',
                                                        'prefer_not_to_say' => 'Prefer not to say',
                                                    ]),
                                                Select::make('language')
                                                    ->options([
                                                        'en' => 'English',
                                                        'es' => 'Spanish',
                                                        'fr' => 'French',
                                                        'de' => 'German',
                                                        'it' => 'Italian',
                                                        'pt' => 'Portuguese',
                                                        'zh' => 'Chinese',
                                                        'ja' => 'Japanese',
                                                        'ko' => 'Korean',
                                                        'ar' => 'Arabic',
                                                    ])
                                                    ->default('en')
                                                    ->searchable(),
                                                Select::make('timezone')
                                                    ->options([
                                                        'UTC' => 'UTC',
                                                        'America/New_York' => 'Eastern Time',
                                                        'America/Chicago' => 'Central Time',
                                                        'America/Denver' => 'Mountain Time',
                                                        'America/Los_Angeles' => 'Pacific Time',
                                                        'Europe/London' => 'London',
                                                        'Europe/Paris' => 'Paris',
                                                        'Asia/Tokyo' => 'Tokyo',
                                                        'Asia/Shanghai' => 'Shanghai',
                                                        'Australia/Sydney' => 'Sydney',
                                                    ])
                                                    ->default('UTC')
                                                    ->searchable(),
                                            ]),
                                        Textarea::make('bio')
                                            ->maxLength(500)
                                            ->rows(3),
                                        Forms\Components\FileUpload::make('avatar_url')
                                            ->label('Profile Picture')
                                            ->image()
                                            ->imageEditor()
                                            ->circleCropper()
                                            ->columnSpanFull(),
                                    ]),
                            ]),

                        Tabs\Tab::make('Contact & Address')
                            ->schema([
                                Section::make('Contact Information')
                                    ->schema([
                                        TextInput::make('website')
                                            ->url()
                                            ->maxLength(255),
                                    ]),
                                Section::make('Address')
                                    ->schema([
                                        Textarea::make('address')
                                            ->rows(2)
                                            ->columnSpanFull(),
                                        Grid::make(3)
                                            ->schema([
                                                TextInput::make('city')
                                                    ->maxLength(100),
                                                TextInput::make('state')
                                                    ->maxLength(100),
                                                TextInput::make('postal_code')
                                                    ->maxLength(20),
                                            ]),
                                        TextInput::make('country')
                                            ->maxLength(100),
                                    ]),
                            ]),

                        Tabs\Tab::make('Professional')
                            ->schema([
                                Section::make('Work Information')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                TextInput::make('department')
                                                    ->maxLength(100),
                                                TextInput::make('position')
                                                    ->maxLength(100),
                                            ]),
                                        Grid::make(2)
                                            ->schema([
                                                TextInput::make('employee_id')
                                                    ->unique(ignoreRecord: true)
                                                    ->maxLength(50),
                                                DatePicker::make('hire_date'),
                                            ]),
                                        Select::make('manager_id')
                                            ->relationship('manager', 'name')
                                            ->searchable()
                                            ->preload(),
                                    ]),
                            ]),

                        Tabs\Tab::make('Preferences')
                            ->schema([
                                Section::make('Notification Preferences')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                Toggle::make('notification_preferences.email_login_alerts')
                                                    ->label('Email Login Alerts')
                                                    ->default(true),
                                                Toggle::make('notification_preferences.email_password_changes')
                                                    ->label('Email Password Changes')
                                                    ->default(true),
                                            ]),
                                        Grid::make(2)
                                            ->schema([
                                                Toggle::make('notification_preferences.email_system_notifications')
                                                    ->label('System Notifications')
                                                    ->default(true),
                                                Toggle::make('notification_preferences.push_notifications')
                                                    ->label('Push Notifications')
                                                    ->default(true),
                                            ]),
                                    ]),
                                Section::make('Privacy Settings')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                Toggle::make('privacy_settings.profile_visible_to_public')
                                                    ->label('Profile Visible to Public')
                                                    ->default(false),
                                                Toggle::make('privacy_settings.email_visible_to_users')
                                                    ->label('Email Visible to Users')
                                                    ->default(false),
                                            ]),
                                        Grid::make(2)
                                            ->schema([
                                                Toggle::make('privacy_settings.phone_visible_to_users')
                                                    ->label('Phone Visible to Users')
                                                    ->default(false),
                                                Toggle::make('privacy_settings.show_online_status')
                                                    ->label('Show Online Status')
                                                    ->default(true),
                                            ]),
                                    ]),
                            ]),
                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('avatar_url')
                    ->label('Avatar')
                    ->circular()
                    ->defaultImageUrl(fn ($record) => 'https://ui-avatars.com/api/?name=' . urlencode($record->name) . '&color=7F9CF5&background=EBF4FF')
                    ->size(40),
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->weight('medium'),
                TextColumn::make('email')
                    ->searchable()
                    ->sortable()
                    ->copyable(),
                TextColumn::make('profile_completion_percentage')
                    ->label('Profile %')
                    ->formatStateUsing(fn ($record) => $record->getProfileCompletionPercentage() . '%')
                    ->color(fn ($record) => $record->getProfileCompletionPercentage() >= 70 ? 'success' : 'warning')
                    ->sortable(),
                IconColumn::make('profile_completed')
                    ->label('Complete')
                    ->boolean()
                    ->getStateUsing(fn ($record) => $record->isProfileComplete()),
                TextColumn::make('department')
                    ->searchable()
                    ->toggleable(),
                TextColumn::make('position')
                    ->searchable()
                    ->toggleable(),
                TextColumn::make('last_login_at')
                    ->label('Last Active')
                    ->dateTime()
                    ->since()
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('department')
                    ->options(fn () => User::whereNotNull('department')->distinct()->pluck('department', 'department')->toArray()),
                SelectFilter::make('profile_complete')
                    ->options([
                        'complete' => 'Complete',
                        'incomplete' => 'Incomplete',
                    ])
                    ->query(function ($query, $data) {
                        if ($data['value'] === 'complete') {
                            return $query->whereNotNull('profile_completed_at');
                        } elseif ($data['value'] === 'incomplete') {
                            return $query->whereNull('profile_completed_at');
                        }
                        return $query;
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('markComplete')
                    ->label('Mark Complete')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn ($record) => !$record->isProfileComplete())
                    ->action(function ($record) {
                        $record->markProfileAsComplete();
                        Notification::make()
                            ->title('Profile marked as complete')
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUserProfiles::route('/'),
            'create' => Pages\CreateUserProfile::route('/create'),
            'view' => Pages\ViewUserProfile::route('/{record}'),
            'edit' => Pages\EditUserProfile::route('/{record}/edit'),
        ];
    }
}
