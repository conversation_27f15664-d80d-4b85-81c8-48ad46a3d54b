<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\UserSessionResource\Pages;
use App\Models\UserSession;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\DatePicker;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

final class UserSessionResource extends Resource
{
    protected static ?string $model = UserSession::class;

    protected static ?string $navigationIcon = 'heroicon-o-computer-desktop';

    protected static ?string $navigationGroup = 'User Management';

    protected static ?string $navigationLabel = 'User Sessions';

    protected static ?string $modelLabel = 'User Session';

    protected static ?string $pluralModelLabel = 'User Sessions';

    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->relationship('user', 'name')
                    ->required()
                    ->searchable()
                    ->preload(),
                Forms\Components\TextInput::make('session_id')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('ip_address')
                    ->required()
                    ->maxLength(45),
                Forms\Components\Textarea::make('user_agent')
                    ->rows(3),
                Forms\Components\DateTimePicker::make('login_at')
                    ->required(),
                Forms\Components\DateTimePicker::make('logout_at'),
                Forms\Components\DateTimePicker::make('last_activity')
                    ->required(),
                Forms\Components\Toggle::make('is_active')
                    ->default(true),
                Forms\Components\TextInput::make('location_country')
                    ->maxLength(255),
                Forms\Components\TextInput::make('location_region')
                    ->maxLength(255),
                Forms\Components\TextInput::make('location_city')
                    ->maxLength(255),
                Forms\Components\Select::make('device_type')
                    ->options([
                        'Desktop' => 'Desktop',
                        'Mobile' => 'Mobile',
                        'Tablet' => 'Tablet',
                    ]),
                Forms\Components\TextInput::make('browser')
                    ->maxLength(255),
                Forms\Components\TextInput::make('operating_system')
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('user.name')
                    ->label('User')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('ip_address')
                    ->label('IP Address')
                    ->searchable()
                    ->copyable(),
                TextColumn::make('device_info')
                    ->label('Device')
                    ->formatStateUsing(function ($record) {
                        $info = $record->device_info;
                        return "{$info['device']} • {$info['browser']} • {$info['os']}";
                    })
                    ->icon(fn ($record) => $record->device_icon)
                    ->tooltip(function ($record) {
                        $info = $record->device_info;
                        return "Device: {$info['device']}\nBrowser: {$info['browser']}\nOS: {$info['os']}";
                    }),
                TextColumn::make('location')
                    ->label('Location')
                    ->placeholder('Unknown')
                    ->toggleable(),
                TextColumn::make('status')
                    ->badge()
                    ->color(fn ($record) => $record->status_color)
                    ->icon(function ($record) {
                        return match ($record->status) {
                            'Active' => 'heroicon-o-check-circle',
                            'Expired' => 'heroicon-o-clock',
                            'Ended' => 'heroicon-o-x-circle',
                            default => 'heroicon-o-question-mark-circle',
                        };
                    }),
                IconColumn::make('is_current')
                    ->label('Current')
                    ->boolean()
                    ->getStateUsing(fn ($record) => $record->isCurrentSession())
                    ->toggleable(),
                IconColumn::make('is_suspicious')
                    ->label('Suspicious')
                    ->boolean()
                    ->getStateUsing(fn ($record) => $record->isSuspicious())
                    ->color('danger')
                    ->toggleable(),
                TextColumn::make('login_at')
                    ->label('Login Time')
                    ->dateTime()
                    ->sortable()
                    ->since()
                    ->tooltip(fn ($record) => $record->login_at->format('Y-m-d H:i:s')),
                TextColumn::make('last_activity')
                    ->label('Last Activity')
                    ->dateTime()
                    ->sortable()
                    ->since()
                    ->tooltip(fn ($record) => $record->last_activity->format('Y-m-d H:i:s')),
                TextColumn::make('formatted_duration')
                    ->label('Duration')
                    ->toggleable(),
            ])
            ->filters([
                SelectFilter::make('user_id')
                    ->label('User')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload(),
                SelectFilter::make('is_active')
                    ->label('Status')
                    ->options([
                        '1' => 'Active',
                        '0' => 'Ended',
                    ]),
                SelectFilter::make('device_type')
                    ->options([
                        'Desktop' => 'Desktop',
                        'Mobile' => 'Mobile',
                        'Tablet' => 'Tablet',
                    ]),
                SelectFilter::make('browser')
                    ->options([
                        'Chrome' => 'Chrome',
                        'Firefox' => 'Firefox',
                        'Safari' => 'Safari',
                        'Edge' => 'Edge',
                    ]),
                Filter::make('current_session')
                    ->query(function (Builder $query): Builder {
                        return $query->where('session_id', session()->getId());
                    })
                    ->label('Current Session'),
                Filter::make('suspicious')
                    ->query(function (Builder $query): Builder {
                        // This would need more complex logic
                        return $query->where('is_active', true);
                    })
                    ->label('Suspicious Sessions'),
                Filter::make('recent')
                    ->query(fn (Builder $query): Builder => $query->recent(7))
                    ->label('Last 7 Days'),
                Filter::make('date_range')
                    ->form([
                        DatePicker::make('from')
                            ->label('From Date'),
                        DatePicker::make('until')
                            ->label('Until Date'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('login_at', '>=', $date),
                            )
                            ->when(
                                $data['until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('login_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\Action::make('end_session')
                    ->label('End Session')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->visible(fn ($record) => $record->is_active && !$record->isCurrentSession())
                    ->action(function ($record) {
                        app(\App\Services\SessionManagementService::class)->endSession($record->session_id);
                        $record->refresh();
                        
                        \Filament\Notifications\Notification::make()
                            ->title('Session Ended')
                            ->body('The session has been terminated')
                            ->success()
                            ->send();
                    }),
                Tables\Actions\Action::make('end_all_user_sessions')
                    ->label('End All User Sessions')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('End All Sessions')
                    ->modalDescription('This will end all active sessions for this user except the current one.')
                    ->action(function ($record) {
                        $count = app(\App\Services\SessionManagementService::class)
                            ->endAllUserSessions($record->user, session()->getId());
                        
                        \Filament\Notifications\Notification::make()
                            ->title('Sessions Ended')
                            ->body("Ended {$count} active sessions")
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('end_sessions')
                        ->label('End Selected Sessions')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->action(function ($records) {
                            $sessionService = app(\App\Services\SessionManagementService::class);
                            $count = 0;
                            
                            foreach ($records as $record) {
                                if ($record->is_active && !$record->isCurrentSession()) {
                                    $sessionService->endSession($record->session_id);
                                    $count++;
                                }
                            }
                            
                            \Filament\Notifications\Notification::make()
                                ->title('Sessions Ended')
                                ->body("Ended {$count} sessions")
                                ->success()
                                ->send();
                        }),
                ]),
            ])
            ->defaultSort('login_at', 'desc')
            ->poll('30s') // Auto-refresh every 30 seconds
            ->persistSortInSession()
            ->persistSearchInSession()
            ->persistFiltersInSession();
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUserSessions::route('/'),
            'view' => Pages\ViewUserSession::route('/{record}'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return (string) static::getModel()::where('is_active', true)->count();
    }

    public static function getNavigationBadgeColor(): ?string
    {
        $activeCount = static::getModel()::where('is_active', true)->count();
        
        if ($activeCount > 100) {
            return 'danger';
        } elseif ($activeCount > 50) {
            return 'warning';
        }
        
        return 'success';
    }
}
