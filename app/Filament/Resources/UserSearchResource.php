<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\UserSearchResource\Pages;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\ImageColumn;
use Illuminate\Database\Eloquent\Builder;

final class UserSearchResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-magnifying-glass';

    protected static ?string $navigationGroup = 'User Management';

    protected static ?string $navigationLabel = 'Advanced Search';

    protected static ?string $modelLabel = 'User Search';

    protected static ?string $pluralModelLabel = 'User Search';

    protected static ?int $navigationSort = 5;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Search Criteria')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('search_name')
                                    ->label('Name')
                                    ->placeholder('Search by name'),
                                Forms\Components\TextInput::make('search_email')
                                    ->label('Email')
                                    ->placeholder('Search by email'),
                                Forms\Components\TextInput::make('search_phone')
                                    ->label('Phone')
                                    ->placeholder('Search by phone'),
                            ]),
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('search_employee_id')
                                    ->label('Employee ID')
                                    ->placeholder('Search by employee ID'),
                                Forms\Components\Select::make('search_department')
                                    ->label('Department')
                                    ->options(fn () => User::whereNotNull('department')->distinct()->pluck('department', 'department')->toArray())
                                    ->searchable(),
                                Forms\Components\Select::make('search_position')
                                    ->label('Position')
                                    ->options(fn () => User::whereNotNull('position')->distinct()->pluck('position', 'position')->toArray())
                                    ->searchable(),
                            ]),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('search_account_status')
                                    ->label('Account Status')
                                    ->options([
                                        'active' => 'Active',
                                        'inactive' => 'Inactive',
                                        'suspended' => 'Suspended',
                                        'pending' => 'Pending',
                                        'locked' => 'Locked',
                                    ])
                                    ->multiple(),
                                Forms\Components\Select::make('search_roles')
                                    ->label('Roles')
                                    ->relationship('roles', 'name')
                                    ->multiple()
                                    ->preload(),
                            ]),
                    ]),
                Forms\Components\Section::make('Date Filters')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DatePicker::make('registered_from')
                                    ->label('Registered From'),
                                Forms\Components\DatePicker::make('registered_to')
                                    ->label('Registered To'),
                            ]),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DatePicker::make('last_login_from')
                                    ->label('Last Login From'),
                                Forms\Components\DatePicker::make('last_login_to')
                                    ->label('Last Login To'),
                            ]),
                    ]),
                Forms\Components\Section::make('Demographics')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\Select::make('search_gender')
                                    ->label('Gender')
                                    ->options([
                                        'male' => 'Male',
                                        'female' => 'Female',
                                        'other' => 'Other',
                                        'prefer_not_to_say' => 'Prefer not to say',
                                    ])
                                    ->multiple(),
                                Forms\Components\TextInput::make('min_age')
                                    ->label('Minimum Age')
                                    ->numeric()
                                    ->minValue(0)
                                    ->maxValue(120),
                                Forms\Components\TextInput::make('max_age')
                                    ->label('Maximum Age')
                                    ->numeric()
                                    ->minValue(0)
                                    ->maxValue(120),
                            ]),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('search_country')
                                    ->label('Country')
                                    ->options(fn () => User::whereNotNull('country')->distinct()->pluck('country', 'country')->toArray())
                                    ->searchable()
                                    ->multiple(),
                                Forms\Components\TextInput::make('search_city')
                                    ->label('City')
                                    ->placeholder('Search by city'),
                            ]),
                    ]),
                Forms\Components\Section::make('Security & Verification')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\Checkbox::make('email_verified')
                                    ->label('Email Verified'),
                                Forms\Components\Checkbox::make('phone_verified')
                                    ->label('Phone Verified'),
                                Forms\Components\Checkbox::make('two_factor_enabled')
                                    ->label('2FA Enabled'),
                            ]),
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\Checkbox::make('profile_complete')
                                    ->label('Profile Complete'),
                                Forms\Components\Checkbox::make('has_manager')
                                    ->label('Has Manager'),
                                Forms\Components\Checkbox::make('is_manager')
                                    ->label('Is Manager'),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('avatar_url')
                    ->label('Avatar')
                    ->circular()
                    ->defaultImageUrl(fn ($record) => 'https://ui-avatars.com/api/?name=' . urlencode($record->name) . '&color=7F9CF5&background=EBF4FF')
                    ->size(40),
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->weight('medium'),
                TextColumn::make('email')
                    ->searchable()
                    ->sortable()
                    ->copyable(),
                TextColumn::make('department')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('position')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('account_status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'gray',
                        'suspended', 'locked' => 'danger',
                        'pending' => 'warning',
                        default => 'gray',
                    }),
                TextColumn::make('roles.name')
                    ->badge()
                    ->separator(','),
                TextColumn::make('last_login_at')
                    ->label('Last Login')
                    ->dateTime()
                    ->since()
                    ->sortable(),
            ])
            ->filters([
                // Filters are handled by the search form
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->modifyQueryUsing(function (Builder $query) {
                // Apply search filters from the form
                $data = request()->all();
                
                if (!empty($data['search_name'])) {
                    $query->where('name', 'like', '%' . $data['search_name'] . '%');
                }
                
                if (!empty($data['search_email'])) {
                    $query->where('email', 'like', '%' . $data['search_email'] . '%');
                }
                
                if (!empty($data['search_phone'])) {
                    $query->where('phone', 'like', '%' . $data['search_phone'] . '%');
                }
                
                if (!empty($data['search_employee_id'])) {
                    $query->where('employee_id', 'like', '%' . $data['search_employee_id'] . '%');
                }
                
                if (!empty($data['search_department'])) {
                    $query->where('department', $data['search_department']);
                }
                
                if (!empty($data['search_position'])) {
                    $query->where('position', $data['search_position']);
                }
                
                if (!empty($data['search_account_status'])) {
                    $query->whereIn('account_status', (array) $data['search_account_status']);
                }
                
                if (!empty($data['search_roles'])) {
                    $query->whereHas('roles', function ($q) use ($data) {
                        $q->whereIn('id', (array) $data['search_roles']);
                    });
                }
                
                if (!empty($data['registered_from'])) {
                    $query->whereDate('created_at', '>=', $data['registered_from']);
                }
                
                if (!empty($data['registered_to'])) {
                    $query->whereDate('created_at', '<=', $data['registered_to']);
                }
                
                if (!empty($data['last_login_from'])) {
                    $query->whereDate('last_login_at', '>=', $data['last_login_from']);
                }
                
                if (!empty($data['last_login_to'])) {
                    $query->whereDate('last_login_at', '<=', $data['last_login_to']);
                }
                
                if (!empty($data['search_gender'])) {
                    $query->whereIn('gender', (array) $data['search_gender']);
                }
                
                if (!empty($data['min_age'])) {
                    $query->whereDate('date_of_birth', '<=', now()->subYears($data['min_age']));
                }
                
                if (!empty($data['max_age'])) {
                    $query->whereDate('date_of_birth', '>=', now()->subYears($data['max_age']));
                }
                
                if (!empty($data['search_country'])) {
                    $query->whereIn('country', (array) $data['search_country']);
                }
                
                if (!empty($data['search_city'])) {
                    $query->where('city', 'like', '%' . $data['search_city'] . '%');
                }
                
                if (!empty($data['email_verified'])) {
                    $query->whereNotNull('email_verified_at');
                }
                
                if (!empty($data['phone_verified'])) {
                    $query->whereNotNull('phone_verified_at');
                }
                
                if (!empty($data['two_factor_enabled'])) {
                    $query->where('two_factor_enabled', true);
                }
                
                if (!empty($data['profile_complete'])) {
                    $query->whereNotNull('profile_completed_at');
                }
                
                if (!empty($data['has_manager'])) {
                    $query->whereNotNull('manager_id');
                }
                
                if (!empty($data['is_manager'])) {
                    $query->whereHas('subordinates');
                }
                
                return $query;
            })
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUserSearches::route('/'),
        ];
    }
}
