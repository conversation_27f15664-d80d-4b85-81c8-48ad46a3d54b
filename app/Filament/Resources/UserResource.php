<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Columns\TextColumn;

use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Builder;


final class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'User Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('User Information')
                    ->tabs([
                        Tabs\Tab::make('Basic Information')
                            ->schema([
                                Section::make('Personal Details')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                TextInput::make('name')
                                                    ->required()
                                                    ->maxLength(255)
                                                    ->live(onBlur: true),
                                                TextInput::make('email')
                                                    ->email()
                                                    ->required()
                                                    ->unique(ignoreRecord: true)
                                                    ->maxLength(255),
                                            ]),
                                        Grid::make(2)
                                            ->schema([
                                                TextInput::make('phone')
                                                    ->tel()
                                                    ->maxLength(20),
                                                TextInput::make('employee_id')
                                                    ->unique(ignoreRecord: true)
                                                    ->maxLength(50),
                                            ]),
                                        Grid::make(3)
                                            ->schema([
                                                DatePicker::make('date_of_birth')
                                                    ->maxDate(now()->subYears(16)),
                                                Select::make('gender')
                                                    ->options([
                                                        'male' => 'Male',
                                                        'female' => 'Female',
                                                        'other' => 'Other',
                                                        'prefer_not_to_say' => 'Prefer not to say',
                                                    ]),
                                                Select::make('language')
                                                    ->options([
                                                        'en' => 'English',
                                                        'es' => 'Spanish',
                                                        'fr' => 'French',
                                                        'de' => 'German',
                                                        'it' => 'Italian',
                                                        'pt' => 'Portuguese',
                                                        'zh' => 'Chinese',
                                                        'ja' => 'Japanese',
                                                        'ko' => 'Korean',
                                                        'ar' => 'Arabic',
                                                    ])
                                                    ->default('en')
                                                    ->searchable(),
                                            ]),
                                        Textarea::make('bio')
                                            ->maxLength(500)
                                            ->rows(3),
                                        Forms\Components\FileUpload::make('avatar_url')
                                            ->label('Avatar')
                                            ->image()
                                            ->imageEditor()
                                            ->circleCropper()
                                            ->columnSpanFull(),
                                    ]),
                            ]),

                        Tabs\Tab::make('Contact & Address')
                            ->schema([
                                Section::make('Contact Information')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                TextInput::make('website')
                                                    ->url()
                                                    ->maxLength(255),
                                                Select::make('timezone')
                                                    ->options([
                                                        'UTC' => 'UTC',
                                                        'America/New_York' => 'Eastern Time',
                                                        'America/Chicago' => 'Central Time',
                                                        'America/Denver' => 'Mountain Time',
                                                        'America/Los_Angeles' => 'Pacific Time',
                                                        'Europe/London' => 'London',
                                                        'Europe/Paris' => 'Paris',
                                                        'Asia/Tokyo' => 'Tokyo',
                                                        'Asia/Shanghai' => 'Shanghai',
                                                        'Australia/Sydney' => 'Sydney',
                                                    ])
                                                    ->default('UTC')
                                                    ->searchable(),
                                            ]),
                                    ]),
                                Section::make('Address')
                                    ->schema([
                                        Textarea::make('address')
                                            ->rows(2)
                                            ->columnSpanFull(),
                                        Grid::make(3)
                                            ->schema([
                                                TextInput::make('city')
                                                    ->maxLength(100),
                                                TextInput::make('state')
                                                    ->maxLength(100),
                                                TextInput::make('postal_code')
                                                    ->maxLength(20),
                                            ]),
                                        TextInput::make('country')
                                            ->maxLength(100),
                                    ]),
                                Section::make('Social Links')
                                    ->schema([
                                        KeyValue::make('social_links')
                                            ->keyLabel('Platform')
                                            ->valueLabel('URL')
                                            ->addActionLabel('Add social link'),
                                    ]),
                            ]),

                        Tabs\Tab::make('Professional')
                            ->schema([
                                Section::make('Work Information')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                TextInput::make('department')
                                                    ->maxLength(100),
                                                TextInput::make('position')
                                                    ->maxLength(100),
                                            ]),
                                        Grid::make(2)
                                            ->schema([
                                                DatePicker::make('hire_date'),
                                                Select::make('manager_id')
                                                    ->relationship('manager', 'name')
                                                    ->searchable()
                                                    ->preload(),
                                            ]),
                                    ]),
                            ]),

                        Tabs\Tab::make('Security & Account')
                            ->schema([
                                Section::make('Authentication')
                                    ->schema([
                                        TextInput::make('password')
                                            ->password()
                                            ->dehydrateStateUsing(fn ($state) => filled($state) ? Hash::make($state) : null)
                                            ->dehydrated(fn ($state) => filled($state))
                                            ->required(fn (string $context): bool => $context === 'create')
                                            ->maxLength(255)
                                            ->revealable(),
                                        Grid::make(2)
                                            ->schema([
                                                Toggle::make('must_change_password')
                                                    ->label('Must change password on next login'),
                                                Toggle::make('two_factor_enabled')
                                                    ->label('Two-factor authentication enabled'),
                                            ]),
                                    ]),
                                Section::make('Account Status')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                Select::make('account_status')
                                                    ->options([
                                                        'active' => 'Active',
                                                        'inactive' => 'Inactive',
                                                        'suspended' => 'Suspended',
                                                        'pending' => 'Pending',
                                                        'locked' => 'Locked',
                                                    ])
                                                    ->default('pending')
                                                    ->required(),
                                                Toggle::make('active_status')
                                                    ->label('Active Status')
                                                    ->default(true),
                                            ]),
                                    ]),
                                Section::make('Preferences')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                Toggle::make('dark_mode')
                                                    ->label('Dark mode preference'),
                                                TextInput::make('messenger_color')
                                                    ->label('Messenger color')
                                                    ->maxLength(7),
                                            ]),
                                        KeyValue::make('notification_preferences')
                                            ->keyLabel('Notification Type')
                                            ->valueLabel('Enabled')
                                            ->addActionLabel('Add notification preference'),
                                        KeyValue::make('privacy_settings')
                                            ->keyLabel('Privacy Setting')
                                            ->valueLabel('Value')
                                            ->addActionLabel('Add privacy setting'),
                                    ]),
                            ]),

                        Tabs\Tab::make('Roles & Permissions')
                            ->schema([
                                Section::make('Role Assignment')
                                    ->schema([
                                        Select::make('roles')
                                            ->label('Roles')
                                            ->multiple()
                                            ->relationship('roles', 'name')
                                            ->preload()
                                            ->searchable()
                                            ->columnSpanFull(),
                                    ]),
                            ]),
                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('avatar_url')
                    ->label('Avatar')
                    ->circular()
                    ->defaultImageUrl(fn ($record) => 'https://ui-avatars.com/api/?name=' . urlencode($record->name) . '&color=7F9CF5&background=EBF4FF')
                    ->size(40),
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->weight('medium'),
                TextColumn::make('email')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->icon('heroicon-m-envelope'),
                TextColumn::make('phone')
                    ->searchable()
                    ->toggleable()
                    ->icon('heroicon-m-phone'),
                TextColumn::make('account_status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'gray',
                        'suspended', 'locked' => 'danger',
                        'pending' => 'warning',
                        default => 'gray',
                    })
                    ->icon(fn (string $state): string => match ($state) {
                        'active' => 'heroicon-o-check-circle',
                        'suspended', 'locked' => 'heroicon-o-x-circle',
                        'pending' => 'heroicon-o-clock',
                        'inactive' => 'heroicon-o-minus-circle',
                        default => 'heroicon-o-question-mark-circle',
                    }),
                TextColumn::make('roles.name')
                    ->label('Roles')
                    ->badge()
                    ->searchable()
                    ->separator(','),
                TextColumn::make('department')
                    ->searchable()
                    ->toggleable()
                    ->icon('heroicon-m-building-office'),
                TextColumn::make('position')
                    ->searchable()
                    ->toggleable(),
                IconColumn::make('two_factor_enabled')
                    ->label('2FA')
                    ->boolean()
                    ->toggleable(),
                TextColumn::make('last_login_at')
                    ->label('Last Login')
                    ->dateTime()
                    ->sortable()
                    ->since()
                    ->toggleable(),
                IconColumn::make('active_status')
                    ->label('Active')
                    ->boolean()
                    ->toggleable(),
                TextColumn::make('manager.name')
                    ->label('Manager')
                    ->searchable()
                    ->sortable()
                    ->toggleable()
                    ->placeholder('No manager'),
                TextColumn::make('subordinates_count')
                    ->label('Team Size')
                    ->counts('subordinates')
                    ->sortable()
                    ->toggleable()
                    ->badge()
                    ->color('info'),
                TextColumn::make('age')
                    ->label('Age')
                    ->getStateUsing(fn ($record) => $record->age ? $record->age . ' years' : 'Unknown')
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query->orderBy('date_of_birth', $direction === 'asc' ? 'desc' : 'asc');
                    })
                    ->toggleable(),
                TextColumn::make('time_in_company')
                    ->label('Tenure')
                    ->toggleable()
                    ->placeholder('Unknown'),
                TextColumn::make('profile_completion')
                    ->label('Profile %')
                    ->getStateUsing(fn ($record) => $record->getProfileCompletionPercentage() . '%')
                    ->badge()
                    ->color(fn ($record) => $record->getProfileCompletionPercentage() >= 70 ? 'success' : 'warning')
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        // This would need a more complex query in a real implementation
                        // For now, we'll sort by profile completion timestamp
                        return $query->orderBy('profile_completed_at', $direction);
                    })
                    ->toggleable(),
                IconColumn::make('email_verified')
                    ->label('Email ✓')
                    ->boolean()
                    ->getStateUsing(fn ($record) => !is_null($record->email_verified_at))
                    ->toggleable(),
                IconColumn::make('phone_verified')
                    ->label('Phone ✓')
                    ->boolean()
                    ->getStateUsing(fn ($record) => !is_null($record->phone_verified_at))
                    ->toggleable(),
                TextColumn::make('login_attempts')
                    ->label('Failed Logins')
                    ->badge()
                    ->color(fn ($state) => $state > 0 ? 'danger' : 'success')
                    ->toggleable(),
                TextColumn::make('created_at')
                    ->label('Registered')
                    ->dateTime()
                    ->sortable()
                    ->since()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(fn ($record) => $record->created_at->format('Y-m-d H:i:s')),
                TextColumn::make('updated_at')
                    ->label('Last Updated')
                    ->dateTime()
                    ->sortable()
                    ->since()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(fn ($record) => $record->updated_at->format('Y-m-d H:i:s')),
            ])
            ->filters([
                SelectFilter::make('account_status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'suspended' => 'Suspended',
                        'pending' => 'Pending',
                        'locked' => 'Locked',
                    ])
                    ->multiple(),
                SelectFilter::make('roles')
                    ->relationship('roles', 'name')
                    ->multiple()
                    ->preload(),
                SelectFilter::make('department')
                    ->options(fn () => User::whereNotNull('department')->distinct()->pluck('department', 'department')->toArray()),
                Filter::make('two_factor_enabled')
                    ->query(fn (Builder $query): Builder => $query->where('two_factor_enabled', true))
                    ->label('2FA Enabled'),
                Filter::make('last_login')
                    ->form([
                        DatePicker::make('last_login_from')
                            ->label('Last login from'),
                        DatePicker::make('last_login_until')
                            ->label('Last login until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['last_login_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('last_login_at', '>=', $date),
                            )
                            ->when(
                                $data['last_login_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('last_login_at', '<=', $date),
                            );
                    }),
                Filter::make('profile_incomplete')
                    ->query(fn (Builder $query): Builder => $query->whereNull('profile_completed_at'))
                    ->label('Incomplete Profiles'),
                Filter::make('registration_date')
                    ->form([
                        DatePicker::make('registered_from')
                            ->label('Registered from'),
                        DatePicker::make('registered_until')
                            ->label('Registered until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['registered_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['registered_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
                Filter::make('age_range')
                    ->form([
                        Forms\Components\TextInput::make('min_age')
                            ->label('Minimum Age')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(120),
                        Forms\Components\TextInput::make('max_age')
                            ->label('Maximum Age')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(120),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['min_age'],
                                fn (Builder $query, $age): Builder => $query->whereDate('date_of_birth', '<=', now()->subYears($age)),
                            )
                            ->when(
                                $data['max_age'],
                                fn (Builder $query, $age): Builder => $query->whereDate('date_of_birth', '>=', now()->subYears($age)),
                            );
                    }),
                SelectFilter::make('gender')
                    ->options([
                        'male' => 'Male',
                        'female' => 'Female',
                        'other' => 'Other',
                        'prefer_not_to_say' => 'Prefer not to say',
                    ])
                    ->multiple(),
                SelectFilter::make('country')
                    ->options(fn () => User::whereNotNull('country')->distinct()->pluck('country', 'country')->toArray())
                    ->searchable()
                    ->multiple(),
                SelectFilter::make('position')
                    ->options(fn () => User::whereNotNull('position')->distinct()->pluck('position', 'position')->toArray())
                    ->searchable()
                    ->multiple(),
                Filter::make('has_manager')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('manager_id'))
                    ->label('Has Manager'),
                Filter::make('is_manager')
                    ->query(fn (Builder $query): Builder => $query->whereHas('subordinates'))
                    ->label('Is Manager'),
                Filter::make('verified_users')
                    ->form([
                        Forms\Components\Checkbox::make('email_verified')
                            ->label('Email Verified'),
                        Forms\Components\Checkbox::make('phone_verified')
                            ->label('Phone Verified'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['email_verified'],
                                fn (Builder $query): Builder => $query->whereNotNull('email_verified_at'),
                            )
                            ->when(
                                $data['phone_verified'],
                                fn (Builder $query): Builder => $query->whereNotNull('phone_verified_at'),
                            );
                    }),
                Filter::make('security_features')
                    ->form([
                        Forms\Components\Checkbox::make('two_factor_enabled')
                            ->label('2FA Enabled'),
                        Forms\Components\Checkbox::make('password_expired')
                            ->label('Password Expired'),
                        Forms\Components\Checkbox::make('account_locked')
                            ->label('Account Locked'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['two_factor_enabled'],
                                fn (Builder $query): Builder => $query->where('two_factor_enabled', true),
                            )
                            ->when(
                                $data['password_expired'],
                                fn (Builder $query): Builder => $query->where('password_changed_at', '<', now()->subDays(90)),
                            )
                            ->when(
                                $data['account_locked'],
                                fn (Builder $query): Builder => $query->where('account_status', 'locked')
                                    ->orWhere('locked_until', '>', now()),
                            );
                    }),
                Filter::make('activity_level')
                    ->form([
                        Forms\Components\Select::make('activity_period')
                            ->label('Activity Period')
                            ->options([
                                '1' => 'Last 24 hours',
                                '7' => 'Last 7 days',
                                '30' => 'Last 30 days',
                                '90' => 'Last 90 days',
                            ])
                            ->default('30'),
                        Forms\Components\Select::make('activity_type')
                            ->label('Activity Type')
                            ->options([
                                'login' => 'Has logged in',
                                'no_login' => 'Has not logged in',
                                'active' => 'Recently active',
                                'inactive' => 'Inactive',
                            ])
                            ->default('login'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        $days = (int) ($data['activity_period'] ?? 30);
                        $cutoff = now()->subDays($days);

                        return match ($data['activity_type'] ?? 'login') {
                            'login' => $query->where('last_login_at', '>=', $cutoff),
                            'no_login' => $query->where('last_login_at', '<', $cutoff)->orWhereNull('last_login_at'),
                            'active' => $query->where('last_login_at', '>=', now()->subMinutes(15)),
                            'inactive' => $query->where('last_login_at', '<', now()->subMinutes(15))->orWhereNull('last_login_at'),
                            default => $query,
                        };
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                ActionGroup::make([
                    Action::make('activate')
                        ->label('Activate')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->requiresConfirmation()
                        ->visible(fn (User $record): bool => !$record->isActive())
                        ->action(fn (User $record) => $record->activate()),
                    Action::make('deactivate')
                        ->label('Deactivate')
                        ->icon('heroicon-o-x-circle')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->visible(fn (User $record): bool => $record->isActive())
                        ->action(fn (User $record) => $record->deactivate()),
                    Action::make('suspend')
                        ->label('Suspend')
                        ->icon('heroicon-o-no-symbol')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->visible(fn (User $record): bool => !$record->isSuspended())
                        ->action(fn (User $record) => $record->suspend()),
                    Action::make('unlock')
                        ->label('Unlock')
                        ->icon('heroicon-o-lock-open')
                        ->color('success')
                        ->requiresConfirmation()
                        ->visible(fn (User $record): bool => $record->isLocked())
                        ->action(fn (User $record) => $record->unlock()),
                    Action::make('resetPassword')
                        ->label('Reset Password')
                        ->icon('heroicon-o-key')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->action(function (User $record): void {
                            $newPassword = \Illuminate\Support\Str::random(12);
                            $record->updatePassword($newPassword);

                            Notification::make()
                                ->title('Password Reset')
                                ->body("Password has been reset to: {$newPassword}")
                                ->success()
                                ->sendToDatabase($record);
                        }),
                    Action::make('forcePasswordChange')
                        ->label('Force Password Change')
                        ->icon('heroicon-o-shield-exclamation')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->action(fn (User $record) => $record->update(['must_change_password' => true])),
                    Action::make('sendWelcomeEmail')
                        ->label('Send Welcome Email')
                        ->icon('heroicon-o-envelope')
                        ->color('info')
                        ->action(function (User $record): void {
                            // TODO: Implement welcome email sending
                            Notification::make()
                                ->title('Welcome Email Sent')
                                ->body("Welcome email sent to {$record->email}")
                                ->success()
                                ->send();
                        }),
                    Action::make('viewActivity')
                        ->label('View Activity')
                        ->icon('heroicon-o-eye')
                        ->color('gray')
                        ->action(function (User $record): void {
                            // TODO: Implement activity view functionality
                            Notification::make()
                                ->title('Activity View')
                                ->body("Viewing activity for {$record->name}")
                                ->info()
                                ->send();
                        }),
                ])
                    ->label('Actions')
                    ->icon('heroicon-m-ellipsis-vertical')
                    ->size('sm')
                    ->color('gray'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activate Selected')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->requiresConfirmation()
                        ->action(fn ($records) => $records->each->activate()),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Deactivate Selected')
                        ->icon('heroicon-o-x-circle')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->action(fn ($records) => $records->each->deactivate()),
                    Tables\Actions\BulkAction::make('suspend')
                        ->label('Suspend Selected')
                        ->icon('heroicon-o-no-symbol')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->action(fn ($records) => $records->each->suspend()),
                    Tables\Actions\BulkAction::make('exportSelected')
                        ->label('Export Selected')
                        ->icon('heroicon-o-arrow-down-tray')
                        ->color('info')
                        ->action(function ($records): void {
                            $userIds = $records->pluck('id')->toArray();
                            $exportService = app(\App\Services\UserImportExportService::class);
                            $csv = $exportService->exportToCsv($userIds);

                            $filename = 'users_export_' . now()->format('Y-m-d_H-i-s') . '.csv';

                            // Store the CSV temporarily and provide download link
                            Storage::disk('local')->put('exports/' . $filename, $csv);

                            Notification::make()
                                ->title('Export Complete')
                                ->body("Export file {$filename} has been generated")
                                ->success()
                                ->send();
                        }),
                    Tables\Actions\BulkAction::make('bulkActivate')
                        ->label('Activate Selected')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->requiresConfirmation()
                        ->action(function ($records): void {
                            $userIds = $records->pluck('id')->toArray();
                            $exportService = app(\App\Services\UserImportExportService::class);
                            $results = $exportService->performBulkOperation('activate', $userIds, [], Auth::user());

                            Notification::make()
                                ->title('Bulk Operation Complete')
                                ->body("Activated {$results['success']} users")
                                ->success()
                                ->send();
                        }),
                    Tables\Actions\BulkAction::make('bulkDeactivate')
                        ->label('Deactivate Selected')
                        ->icon('heroicon-o-x-circle')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->action(function ($records): void {
                            $userIds = $records->pluck('id')->toArray();
                            $exportService = app(\App\Services\UserImportExportService::class);
                            $results = $exportService->performBulkOperation('deactivate', $userIds, [], Auth::user());

                            Notification::make()
                                ->title('Bulk Operation Complete')
                                ->body("Deactivated {$results['success']} users")
                                ->success()
                                ->send();
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->persistSortInSession()
            ->persistSearchInSession()
            ->persistFiltersInSession()
            ->searchOnBlur()
            ->searchDebounce('500ms')
            // ->globalSearchAttributes(['name', 'email', 'phone', 'employee_id', 'department', 'position'])
            ->striped()
            ->paginated([10, 25, 50, 100])
            ->extremePaginationLinks();
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'view' => Pages\ViewUser::route('/{record}'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
