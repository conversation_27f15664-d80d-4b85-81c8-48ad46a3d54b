<?php

declare(strict_types=1);

namespace App\Filament\Resources\ClassResource\RelationManagers;

use App\Models\Classes;
use App\Models\ClassEnrollment;
use App\Models\Student;
use App\Models\StudentEnrollment;
use App\Models\SubjectEnrollment;
use App\Services\GeneralSettingsService;
use Exception;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;

final class ClassEnrollmentsRelationManager extends RelationManager
{
    protected static string $relationship = 'class_enrollments';

    protected static ?string $recordTitleAttribute = 'student_id';

    protected static ?string $title = 'Enrolled Students';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('student_id')
                    ->label('Student')
                    ->options(fn () => Student::all()->pluck('full_name', 'id'))
                    ->searchable()
                    ->required()
                    ->preload()
                    ->columnSpan('full'),

                Grid::make(3)
                    ->schema([
                        TextInput::make('prelim_grade')
                            ->label('Prelim')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(100)
                            ->live()
                            ->afterStateUpdated(fn (callable $set) => $set('total_average', null)), // Recalculate average

                        TextInput::make('midterm_grade')
                            ->label('Midterm')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(100)
                            ->live()
                            ->afterStateUpdated(fn (callable $set) => $set('total_average', null)), // Recalculate average

                        TextInput::make('finals_grade')
                            ->label('Finals')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(100)
                            ->live()
                            ->afterStateUpdated(fn (callable $set) => $set('total_average', null)), // Recalculate average
                    ]),

                Grid::make(2)
                    ->schema([
                        TextInput::make('total_average')
                            ->label('Final Grade')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(100)
                            ->disabled()
                            ->placeholder(function (callable $get): string {
                                // Calculate average only if all grades are present
                                $prelim = $get('prelim_grade');
                                $midterm = $get('midterm_grade');
                                $finals = $get('finals_grade');

                                if ($prelim !== null && $midterm !== null && $finals !== null) {
                                    $average = ($prelim + $midterm + $finals) / 3;

                                    return number_format($average, 2);
                                }

                                return 'N/A';
                            }),

                        Select::make('status')
                            ->options([
                                true => 'Passed',
                                false => 'Failed',
                            ])
                            ->default(true),
                    ]),

                Textarea::make('remarks')
                    ->rows(2)
                    ->columnSpan('full'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('student_id')
            ->columns([
                TextColumn::make('student_id')
                    ->label('Student')
                    ->formatStateUsing(fn ($record) => $record->student?->full_name ?? 'N/A')
                    ->searchable(query: fn ($query, $search) => $query->whereHas('student', function ($q) use ($search): void {
                        $q->where('first_name', 'like', "%{$search}%")
                            ->orWhere('last_name', 'like', "%{$search}%");
                    }))
                    ->sortable(),

                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->formatStateUsing(fn ($record): string => $record->status ? 'Enrolled' : 'Not Active')
                    ->color(fn ($record): string => $record->status ? 'success' : 'gray'),

                TextColumn::make('prelim_grade')
                    ->label('Prelim')
                    ->sortable(),

                TextColumn::make('midterm_grade')
                    ->label('Midterm')
                    ->sortable(),

                TextColumn::make('finals_grade')
                    ->label('Finals')
                    ->sortable(),

                TextColumn::make('total_average')
                    ->label('Final Grade')
                    ->sortable(),

                IconColumn::make('status')
                    ->boolean()
                    ->label('Status')
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                TextColumn::make('remarks')
                    ->limit(30)
                    ->tooltip(fn (TextColumn $column): mixed => $column->getState()),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        '1' => 'Passed',
                        '0' => 'Failed',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Enroll Student')
                    ->modalHeading('Enroll New Student')
                    ->modalWidth('lg'),
                Tables\Actions\Action::make('view_pending')
                    ->label('View Pending Students')
                    ->icon('heroicon-o-clock')
                    ->color('warning')
                    ->modalHeading('Pending Students for this Class')
                    ->modalContent(function () {
                        $pendingInfo = $this->getPendingStudentsInfo();

                        if ($pendingInfo['count'] === 0) {
                            return view('filament.components.no-pending-students');
                        }

                        return view('filament.components.pending-students-list', [
                            'students' => $pendingInfo['students'],
                            'count' => $pendingInfo['count'],
                        ]);
                    })
                    ->modalSubmitAction(false)
                    ->modalCancelActionLabel('Close'),
                Tables\Actions\Action::make('re_enroll_failed')
                    ->label('Re-enroll Failed Students')
                    ->icon('heroicon-o-arrow-path')
                    ->color('info')
                    ->requiresConfirmation()
                    ->modalHeading('Re-enroll Students Who Failed Auto-Enrollment')
                    ->modalDescription('This will attempt to re-enroll students who were verified by cashier but failed to be enrolled in this class due to technical issues.')
                    ->action(function (): void {
                        $result = $this->reEnrollFailedStudents();

                        if ($result['success_count'] > 0) {
                            \Filament\Notifications\Notification::make()
                                ->title('Re-enrollment Successful')
                                ->body("Successfully re-enrolled {$result['success_count']} student(s) in this class.")
                                ->success()
                                ->send();
                        }

                        if ($result['error_count'] > 0) {
                            \Filament\Notifications\Notification::make()
                                ->title('Re-enrollment Issues')
                                ->body("Failed to re-enroll {$result['error_count']} student(s). Check logs for details.")
                                ->warning()
                                ->send();
                        }

                        if ($result['success_count'] === 0 && $result['error_count'] === 0) {
                            \Filament\Notifications\Notification::make()
                                ->title('No Students to Re-enroll')
                                ->body('No students found who need re-enrollment in this class.')
                                ->info()
                                ->send();
                        }
                    }),
                Tables\Actions\Action::make('recreate_assessment_pdf')
                    ->label('Recreate Assessment PDFs')
                    ->icon('heroicon-o-document-duplicate')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading('Recreate Assessment PDFs for Enrolled Students')
                    ->modalDescription('This will regenerate assessment PDFs for all students enrolled in this class. This is useful when there are issues with class schedules not showing up correctly in the PDFs.')
                    ->action(function (): void {
                        $result = $this->recreateAssessmentPdfs();

                        if ($result['success_count'] > 0) {
                            \Filament\Notifications\Notification::make()
                                ->title('PDFs Recreated Successfully')
                                ->body("Successfully recreated assessment PDFs for {$result['success_count']} student(s).")
                                ->success()
                                ->send();
                        }

                        if ($result['error_count'] > 0) {
                            \Filament\Notifications\Notification::make()
                                ->title('PDF Recreation Issues')
                                ->body("Failed to recreate PDFs for {$result['error_count']} student(s). Check logs for details.")
                                ->warning()
                                ->send();
                        }

                        if ($result['success_count'] === 0 && $result['error_count'] === 0) {
                            \Filament\Notifications\Notification::make()
                                ->title('No Students Found')
                                ->body('No enrolled students found for this class.')
                                ->info()
                                ->send();
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('Move')
                    ->requiresConfirmation()
                    ->modalHeading('Move This Student to another class')
                    ->modalDescription(
                        'Are you sure you\'d like to Move this student to another Class? This cannot be undone.'
                    )
                    ->icon('heroicon-o-arrow-right-on-rectangle')
                    ->label('Move to a Class')
                    ->form([
                        Select::make('moveClass')
                            ->label('Available Classes')
                            ->hint(
                                'Select a Section you want this student to move to (same subject, current school year & semester)'
                            )
                            ->options(function () {
                                $settingsService = app(GeneralSettingsService::class);
                                $currentSchoolYear = $settingsService->getCurrentSchoolYearString();
                                $currentSemester = $settingsService->getCurrentSemester();
                                $currentClass = $this->getOwnerRecord();

                                return Classes::where('subject_code', $currentClass->subject_code)
                                    ->where('school_year', $currentSchoolYear)
                                    ->where('semester', $currentSemester)
                                    ->whereNot('id', $currentClass->id)
                                    ->get()
                                    ->mapWithKeys(function ($class) {
                                        $enrolledCount = $class->class_enrollments_count ?? $class->class_enrollments()->count();
                                        $maxSlots = $class->maximum_slots ?? 0;
                                        $availableSlots = $maxSlots - $enrolledCount;

                                        $label = "Section {$class->section}";
                                        if ($maxSlots > 0) {
                                            $label .= " ({$enrolledCount}/{$maxSlots} enrolled";
                                            if ($availableSlots > 0) {
                                                $label .= ", {$availableSlots} available)";
                                            } else {
                                                $label .= ", FULL)";
                                            }
                                        }

                                        return [$class->id => $label];
                                    });
                            })
                            ->searchable()
                            ->placeholder('Choose a class section...')
                            ->helperText(function () {
                                $settingsService = app(GeneralSettingsService::class);
                                $currentSchoolYear = $settingsService->getCurrentSchoolYearString();
                                $currentSemester = $settingsService->getCurrentSemester();

                                return "Only showing classes for {$currentSchoolYear}, Semester {$currentSemester} with the same subject.";
                            }),
                    ])
                    ->action(function (array $data, $record): void {
                        $record->class_id = $data['moveClass'];
                        $record->save();

                        \Filament\Notifications\Notification::make()
                            ->title('Student Moved Successfully')
                            ->body('The student has been moved to the selected class.')
                            ->success()
                            ->send();
                    }),
                Tables\Actions\EditAction::make()
                    ->modalHeading('Edit Student Enrollment')
                    ->modalWidth('lg'),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('Move')
                        ->requiresConfirmation()
                        ->modalHeading('Move These Students to another class')
                        ->modalDescription(
                            'Are you sure you\'d like to move these students to another class? This cannot be undone.'
                        )
                        ->icon('heroicon-o-arrow-right-on-rectangle')
                        ->label('Move to a class')
                        ->form([
                            Select::make('moveClass1')
                                ->label('Available Classes')
                                ->options(function () {
                                    $settingsService = app(GeneralSettingsService::class);
                                    $currentSchoolYear = $settingsService->getCurrentSchoolYearString();
                                    $currentSemester = $settingsService->getCurrentSemester();
                                    $currentClass = $this->getOwnerRecord();

                                    return Classes::where('subject_code', $currentClass->subject_code)
                                        ->where('school_year', $currentSchoolYear)
                                        ->where('semester', $currentSemester)
                                        ->whereNot('id', $currentClass->id)
                                        ->get()
                                        ->mapWithKeys(function ($class) {
                                            $enrolledCount = $class->class_enrollments_count ?? $class->class_enrollments()->count();
                                            $maxSlots = $class->maximum_slots ?? 0;
                                            $availableSlots = $maxSlots - $enrolledCount;

                                            $label = "Section {$class->section}";
                                            if ($maxSlots > 0) {
                                                $label .= " ({$enrolledCount}/{$maxSlots} enrolled";
                                                if ($availableSlots > 0) {
                                                    $label .= ", {$availableSlots} available)";
                                                } else {
                                                    $label .= ", FULL)";
                                                }
                                            }

                                            return [$class->id => $label];
                                        });
                                })
                                ->searchable()
                                ->placeholder('Choose a class section...')
                                ->helperText(function () {
                                    $settingsService = app(GeneralSettingsService::class);
                                    $currentSchoolYear = $settingsService->getCurrentSchoolYearString();
                                    $currentSemester = $settingsService->getCurrentSemester();

                                    return "Only showing classes for {$currentSchoolYear}, Semester {$currentSemester} with the same subject.";
                                }),
                        ])
                        ->action(function (array $data, Collection $records): void {
                            $movedCount = 0;
                            $records->each(function ($record) use ($data, &$movedCount): void {
                                $record->update([
                                    'class_id' => $data['moveClass1'],
                                ]);
                                $movedCount++;
                            });

                            \Filament\Notifications\Notification::make()
                                ->title('Students Moved Successfully')
                                ->body("Successfully moved {$movedCount} student(s) to the selected class.")
                                ->success()
                                ->send();
                        }),
                ]),
            ]);
    }

    /**
     * Get pending students for this class
     */
    public function getPendingStudentsInfo(): array
    {
        $settingsService = app(GeneralSettingsService::class);
        $currentSchoolYear = $settingsService->getCurrentSchoolYearString();
        $currentSemester = $settingsService->getCurrentSemester();
        $classId = $this->getOwnerRecord()->id;

        // Get pending students from SubjectEnrollment who are not yet enrolled in this class
        $pendingStudents = SubjectEnrollment::with(['student', 'studentEnrollment'])
            ->where('class_id', $classId)
            ->whereHas('studentEnrollment', function ($query) use ($currentSchoolYear, $currentSemester): void {
                $query->where('status', 'Pending')
                    ->where('school_year', $currentSchoolYear)
                    ->where('semester', $currentSemester);
            })
            ->whereDoesntHave('student.classEnrollments', function ($query) use ($classId): void {
                $query->where('class_id', $classId);
            })
            ->get();

        return [
            'count' => $pendingStudents->count(),
            'students' => $pendingStudents->map(fn ($subjectEnrollment): array => [
                'id' => $subjectEnrollment->student->id,
                'name' => $subjectEnrollment->student->full_name,
                'enrollment_id' => $subjectEnrollment->enrollment_id,
                'subject_enrollment_id' => $subjectEnrollment->id,
            ])->toArray(),
        ];
    }

    /**
     * Re-enroll students who were verified by cashier but failed to be enrolled in this class
     */
    public function reEnrollFailedStudents(): array
    {
        $settingsService = app(GeneralSettingsService::class);
        $currentSchoolYear = $settingsService->getCurrentSchoolYearString();
        $currentSemester = $settingsService->getCurrentSemester();
        $classId = $this->getOwnerRecord()->id;
        $class = $this->getOwnerRecord();

        $successCount = 0;
        $errorCount = 0;
        $errors = [];

        // Find students who should be enrolled in this class but aren't
        // Use a simpler approach to avoid data type issues
        $subjectEnrollments = SubjectEnrollment::where('class_id', $classId)
            ->whereHas('studentEnrollment', function ($query) use ($currentSchoolYear, $currentSemester): void {
                $query->where('status', 'Verified By Cashier')
                    ->where('school_year', $currentSchoolYear)
                    ->where('semester', $currentSemester);
            })
            ->with(['student', 'studentEnrollment'])
            ->get();

        // Filter out students who are already enrolled in this class
        $enrolledStudentIds = ClassEnrollment::where('class_id', $classId)
            ->pluck('student_id')
            ->map(function ($id): string {
                return (string) $id; // Convert to string for comparison
            })
            ->toArray();

        $failedStudents = $subjectEnrollments->filter(fn ($subjectEnrollment): bool => ! in_array((string) $subjectEnrollment->student_id, $enrolledStudentIds));

        foreach ($failedStudents as $subjectEnrollment) {
            try {
                // Create class enrollment directly
                ClassEnrollment::create([
                    'class_id' => $classId,
                    'student_id' => $subjectEnrollment->student_id,
                    'status' => true, // Active enrollment
                ]);

                $successCount++;

                Log::info("Successfully re-enrolled student {$subjectEnrollment->student_id} in class {$classId}", [
                    'student_name' => $subjectEnrollment->student->full_name,
                    'class_subject' => $class->subject_code,
                    'class_section' => $class->section,
                ]);

            } catch (Exception $e) {
                $errorCount++;
                $errorMessage = "Failed to re-enroll student {$subjectEnrollment->student_id}: ".$e->getMessage();
                $errors[] = $errorMessage;

                Log::error($errorMessage, [
                    'student_name' => $subjectEnrollment->student->full_name ?? 'Unknown',
                    'class_id' => $classId,
                    'exception' => $e,
                ]);
            }
        }

        return [
            'success_count' => $successCount,
            'error_count' => $errorCount,
            'errors' => $errors,
            'total_found' => $failedStudents->count(),
        ];
    }

    /**
     * Recreate assessment PDFs for all students enrolled in this class
     */
    public function recreateAssessmentPdfs(): array
    {
        $settingsService = app(GeneralSettingsService::class);
        $currentSchoolYear = $settingsService->getCurrentSchoolYearString();
        $currentSemester = $settingsService->getCurrentSemester();
        $classId = $this->getOwnerRecord()->id;
        $class = $this->getOwnerRecord();

        $successCount = 0;
        $errorCount = 0;
        $errors = [];

        // Find all students enrolled in this class
        $enrolledStudents = ClassEnrollment::where('class_id', $classId)
            ->with(['student'])
            ->get();

        foreach ($enrolledStudents as $classEnrollment) {
            try {
                $student = $classEnrollment->student;

                // Find the student's enrollment record for current period
                $studentEnrollment = StudentEnrollment::withTrashed()
                    ->where('student_id', $student->id)
                    ->where('school_year', $currentSchoolYear)
                    ->where('semester', $currentSemester)
                    ->where('status', 'Verified By Cashier')
                    ->first();

                if (! $studentEnrollment) {
                    $errors[] = "No verified enrollment found for student {$student->full_name} (ID: {$student->id})";
                    $errorCount++;

                    continue;
                }

                // Dispatch the PDF generation job
                \App\Jobs\GenerateAssessmentPdfJob::dispatch($studentEnrollment);

                $successCount++;

                Log::info("Successfully queued assessment PDF recreation for student {$student->id}", [
                    'student_name' => $student->full_name,
                    'enrollment_id' => $studentEnrollment->id,
                    'class_subject' => $class->subject_code,
                    'class_section' => $class->section,
                ]);

            } catch (Exception $e) {
                $errorCount++;
                $errorMessage = "Failed to recreate PDF for student {$classEnrollment->student_id}: ".$e->getMessage();
                $errors[] = $errorMessage;

                Log::error($errorMessage, [
                    'student_name' => $classEnrollment->student->full_name ?? 'Unknown',
                    'class_id' => $classId,
                    'exception' => $e,
                ]);
            }
        }

        return [
            'success_count' => $successCount,
            'error_count' => $errorCount,
            'errors' => $errors,
            'total_found' => $enrolledStudents->count(),
        ];
    }
}
