<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_sessions', function (Blueprint $table): void {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('session_id')->index();
            $table->ipAddress('ip_address');
            $table->text('user_agent')->nullable();
            
            // Session timing
            $table->timestamp('login_at');
            $table->timestamp('logout_at')->nullable();
            $table->timestamp('last_activity');
            $table->boolean('is_active')->default(true)->index();
            
            // Location information
            $table->string('location_country')->nullable();
            $table->string('location_region')->nullable();
            $table->string('location_city')->nullable();
            
            // Device information
            $table->string('device_type')->nullable(); // Mobile, Tablet, Desktop
            $table->string('browser')->nullable();
            $table->string('operating_system')->nullable();
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['user_id', 'is_active']);
            $table->index(['user_id', 'login_at']);
            $table->index(['session_id', 'is_active']);
            $table->index(['ip_address', 'login_at']);
            $table->index(['last_activity']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_sessions');
    }
};
