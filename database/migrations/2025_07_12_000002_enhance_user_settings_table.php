<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_settings', function (Blueprint $table): void {
            // Theme and Display Preferences
            if (!Schema::hasColumn('user_settings', 'theme_preference')) {
                $table->enum('theme_preference', ['light', 'dark', 'system'])->default('system')->after('school_year_start');
            }
            if (!Schema::hasColumn('user_settings', 'language_preference')) {
                $table->string('language_preference', 5)->default('en')->after('theme_preference');
            }
            if (!Schema::hasColumn('user_settings', 'timezone_preference')) {
                $table->string('timezone_preference')->default('UTC')->after('language_preference');
            }

            // Notification Preferences
            if (!Schema::hasColumn('user_settings', 'email_notifications')) {
                $table->boolean('email_notifications')->default(true)->after('timezone_preference');
            }
            if (!Schema::hasColumn('user_settings', 'push_notifications')) {
                $table->boolean('push_notifications')->default(true)->after('email_notifications');
            }
            if (!Schema::hasColumn('user_settings', 'sms_notifications')) {
                $table->boolean('sms_notifications')->default(false)->after('push_notifications');
            }
            if (!Schema::hasColumn('user_settings', 'desktop_notifications')) {
                $table->boolean('desktop_notifications')->default(true)->after('sms_notifications');
            }
            if (!Schema::hasColumn('user_settings', 'notification_frequency')) {
                $table->enum('notification_frequency', ['immediate', 'hourly', 'daily', 'weekly'])->default('immediate')->after('desktop_notifications');
            }

            // Privacy Settings
            if (!Schema::hasColumn('user_settings', 'privacy_profile_visible')) {
                $table->boolean('privacy_profile_visible')->default(false)->after('notification_frequency');
            }
            if (!Schema::hasColumn('user_settings', 'privacy_email_visible')) {
                $table->boolean('privacy_email_visible')->default(false)->after('privacy_profile_visible');
            }
            if (!Schema::hasColumn('user_settings', 'privacy_phone_visible')) {
                $table->boolean('privacy_phone_visible')->default(false)->after('privacy_email_visible');
            }
            if (!Schema::hasColumn('user_settings', 'privacy_show_online_status')) {
                $table->boolean('privacy_show_online_status')->default(true)->after('privacy_phone_visible');
            }
            if (!Schema::hasColumn('user_settings', 'privacy_allow_direct_messages')) {
                $table->boolean('privacy_allow_direct_messages')->default(true)->after('privacy_show_online_status');
            }
            if (!Schema::hasColumn('user_settings', 'privacy_show_in_directory')) {
                $table->boolean('privacy_show_in_directory')->default(true)->after('privacy_allow_direct_messages');
            }

            // Security Settings
            if (!Schema::hasColumn('user_settings', 'security_two_factor_enabled')) {
                $table->boolean('security_two_factor_enabled')->default(false)->after('privacy_show_in_directory');
            }
            if (!Schema::hasColumn('user_settings', 'security_session_timeout')) {
                $table->integer('security_session_timeout')->default(120)->after('security_two_factor_enabled'); // minutes
            }
            if (!Schema::hasColumn('user_settings', 'security_login_alerts')) {
                $table->boolean('security_login_alerts')->default(true)->after('security_session_timeout');
            }

            // Dashboard and UI Preferences
            if (!Schema::hasColumn('user_settings', 'dashboard_layout')) {
                $table->string('dashboard_layout')->default('default')->after('security_login_alerts');
            }
            if (!Schema::hasColumn('user_settings', 'dashboard_widgets')) {
                $table->json('dashboard_widgets')->nullable()->after('dashboard_layout');
            }
            if (!Schema::hasColumn('user_settings', 'table_preferences')) {
                $table->json('table_preferences')->nullable()->after('dashboard_widgets');
            }
            if (!Schema::hasColumn('user_settings', 'sidebar_collapsed')) {
                $table->boolean('sidebar_collapsed')->default(false)->after('table_preferences');
            }
            if (!Schema::hasColumn('user_settings', 'items_per_page')) {
                $table->integer('items_per_page')->default(25)->after('sidebar_collapsed');
            }

            // Format Preferences
            if (!Schema::hasColumn('user_settings', 'date_format')) {
                $table->string('date_format')->default('Y-m-d')->after('items_per_page');
            }
            if (!Schema::hasColumn('user_settings', 'time_format')) {
                $table->string('time_format')->default('H:i:s')->after('date_format');
            }
            if (!Schema::hasColumn('user_settings', 'currency_format')) {
                $table->string('currency_format')->default('USD')->after('time_format');
            }
            if (!Schema::hasColumn('user_settings', 'number_format')) {
                $table->string('number_format')->default('en_US')->after('currency_format');
            }

            // Custom Settings
            if (!Schema::hasColumn('user_settings', 'custom_settings')) {
                $table->json('custom_settings')->nullable()->after('number_format');
            }

            // Add indexes for performance
            $table->index(['theme_preference']);
            $table->index(['language_preference']);
            $table->index(['email_notifications']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_settings', function (Blueprint $table): void {
            $columnsToRemove = [
                'theme_preference', 'language_preference', 'timezone_preference',
                'email_notifications', 'push_notifications', 'sms_notifications',
                'desktop_notifications', 'notification_frequency',
                'privacy_profile_visible', 'privacy_email_visible', 'privacy_phone_visible',
                'privacy_show_online_status', 'privacy_allow_direct_messages', 'privacy_show_in_directory',
                'security_two_factor_enabled', 'security_session_timeout', 'security_login_alerts',
                'dashboard_layout', 'dashboard_widgets', 'table_preferences',
                'sidebar_collapsed', 'items_per_page',
                'date_format', 'time_format', 'currency_format', 'number_format',
                'custom_settings'
            ];

            foreach ($columnsToRemove as $column) {
                if (Schema::hasColumn('user_settings', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
