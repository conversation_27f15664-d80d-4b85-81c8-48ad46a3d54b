<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_notification_preferences', function (Blueprint $table): void {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            
            $table->string('notification_type'); // security_alerts, login_notifications, etc.
            $table->string('channel'); // email, sms, push, in_app, desktop
            $table->boolean('enabled')->default(true);
            $table->enum('frequency', ['immediate', 'hourly', 'daily', 'weekly', 'monthly'])->default('immediate');
            
            // Quiet hours
            $table->time('quiet_hours_start')->nullable();
            $table->time('quiet_hours_end')->nullable();
            $table->string('timezone')->default('UTC');
            
            // Additional settings (JSON)
            $table->json('settings')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['user_id', 'notification_type']);
            $table->index(['user_id', 'channel']);
            $table->index(['notification_type', 'channel']);
            $table->index(['enabled']);
            $table->index(['frequency']);
            
            // Unique constraint to prevent duplicates
            $table->unique(['user_id', 'notification_type', 'channel']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_notification_preferences');
    }
};
